{"ast": null, "code": "import { px } from 'motion-dom';\nconst dashKeys = {\n  offset: \"stroke-dashoffset\",\n  array: \"stroke-dasharray\"\n};\nconst camelKeys = {\n  offset: \"strokeDashoffset\",\n  array: \"strokeDasharray\"\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n  // Normalise path length by setting SVG attribute pathLength to 1\n  attrs.pathLength = 1;\n  // We use dash case when setting attributes directly to the DOM node and camel case\n  // when defining props on a React component.\n  const keys = useDashCase ? dashKeys : camelKeys;\n  // Build the dash offset\n  attrs[keys.offset] = px.transform(-offset);\n  // Build the dash array\n  const pathLength = px.transform(length);\n  const pathSpacing = px.transform(spacing);\n  attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\nexport { buildSVGPath };", "map": {"version": 3, "names": ["px", "dashKeys", "offset", "array", "camel<PERSON><PERSON>s", "buildSVGPath", "attrs", "length", "spacing", "useDashCase", "<PERSON><PERSON><PERSON><PERSON>", "keys", "transform", "pathSpacing"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs"], "sourcesContent": ["import { px } from 'motion-dom';\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = px.transform(-offset);\n    // Build the dash array\n    const pathLength = px.transform(length);\n    const pathSpacing = px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\nexport { buildSVGPath };\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,YAAY;AAE/B,MAAMC,QAAQ,GAAG;EACbC,MAAM,EAAE,mBAAmB;EAC3BC,KAAK,EAAE;AACX,CAAC;AACD,MAAMC,SAAS,GAAG;EACdF,MAAM,EAAE,kBAAkB;EAC1BC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,GAAG,CAAC,EAAEN,MAAM,GAAG,CAAC,EAAEO,WAAW,GAAG,IAAI,EAAE;EAC9E;EACAH,KAAK,CAACI,UAAU,GAAG,CAAC;EACpB;EACA;EACA,MAAMC,IAAI,GAAGF,WAAW,GAAGR,QAAQ,GAAGG,SAAS;EAC/C;EACAE,KAAK,CAACK,IAAI,CAACT,MAAM,CAAC,GAAGF,EAAE,CAACY,SAAS,CAAC,CAACV,MAAM,CAAC;EAC1C;EACA,MAAMQ,UAAU,GAAGV,EAAE,CAACY,SAAS,CAACL,MAAM,CAAC;EACvC,MAAMM,WAAW,GAAGb,EAAE,CAACY,SAAS,CAACJ,OAAO,CAAC;EACzCF,KAAK,CAACK,IAAI,CAACR,KAAK,CAAC,GAAG,GAAGO,UAAU,IAAIG,WAAW,EAAE;AACtD;AAEA,SAASR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}