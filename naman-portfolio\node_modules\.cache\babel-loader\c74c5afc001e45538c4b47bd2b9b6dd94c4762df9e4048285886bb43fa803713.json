{"ast": null, "code": "import { isSVGElement, isSVGSVGElement } from 'motion-dom';\nimport { HTMLVisualElement } from '../../render/html/HTMLVisualElement.mjs';\nimport { ObjectVisualElement } from '../../render/object/ObjectVisualElement.mjs';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { SVGVisualElement } from '../../render/svg/SVGVisualElement.mjs';\nfunction createDOMVisualElement(element) {\n  const options = {\n    presenceContext: null,\n    props: {},\n    visualState: {\n      renderState: {\n        transform: {},\n        transformOrigin: {},\n        style: {},\n        vars: {},\n        attrs: {}\n      },\n      latestValues: {}\n    }\n  };\n  const node = isSVGElement(element) && !isSVGSVGElement(element) ? new SVGVisualElement(options) : new HTMLVisualElement(options);\n  node.mount(element);\n  visualElementStore.set(element, node);\n}\nfunction createObjectVisualElement(subject) {\n  const options = {\n    presenceContext: null,\n    props: {},\n    visualState: {\n      renderState: {\n        output: {}\n      },\n      latestValues: {}\n    }\n  };\n  const node = new ObjectVisualElement(options);\n  node.mount(subject);\n  visualElementStore.set(subject, node);\n}\nexport { createDOMVisualElement, createObjectVisualElement };", "map": {"version": 3, "names": ["isSVGElement", "isSVGSVGElement", "HTMLVisualElement", "ObjectVisualElement", "visualElementStore", "SVGVisualElement", "createDOMVisualElement", "element", "options", "presenceContext", "props", "visualState", "renderState", "transform", "transform<PERSON><PERSON>in", "style", "vars", "attrs", "latestValues", "node", "mount", "set", "createObjectVisualElement", "subject", "output"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs"], "sourcesContent": ["import { isSVGElement, isSVGSVGElement } from 'motion-dom';\nimport { HTMLVisualElement } from '../../render/html/HTMLVisualElement.mjs';\nimport { ObjectVisualElement } from '../../render/object/ObjectVisualElement.mjs';\nimport { visualElementStore } from '../../render/store.mjs';\nimport { SVGVisualElement } from '../../render/svg/SVGVisualElement.mjs';\n\nfunction createDOMVisualElement(element) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                transform: {},\n                transformOrigin: {},\n                style: {},\n                vars: {},\n                attrs: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = isSVGElement(element) && !isSVGSVGElement(element)\n        ? new SVGVisualElement(options)\n        : new HTMLVisualElement(options);\n    node.mount(element);\n    visualElementStore.set(element, node);\n}\nfunction createObjectVisualElement(subject) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                output: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = new ObjectVisualElement(options);\n    node.mount(subject);\n    visualElementStore.set(subject, node);\n}\n\nexport { createDOMVisualElement, createObjectVisualElement };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,eAAe,QAAQ,YAAY;AAC1D,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,mBAAmB,QAAQ,6CAA6C;AACjF,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,uCAAuC;AAExE,SAASC,sBAAsBA,CAACC,OAAO,EAAE;EACrC,MAAMC,OAAO,GAAG;IACZC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,CAAC,CAAC;IACTC,WAAW,EAAE;MACTC,WAAW,EAAE;QACTC,SAAS,EAAE,CAAC,CAAC;QACbC,eAAe,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC;QACTC,IAAI,EAAE,CAAC,CAAC;QACRC,KAAK,EAAE,CAAC;MACZ,CAAC;MACDC,YAAY,EAAE,CAAC;IACnB;EACJ,CAAC;EACD,MAAMC,IAAI,GAAGnB,YAAY,CAACO,OAAO,CAAC,IAAI,CAACN,eAAe,CAACM,OAAO,CAAC,GACzD,IAAIF,gBAAgB,CAACG,OAAO,CAAC,GAC7B,IAAIN,iBAAiB,CAACM,OAAO,CAAC;EACpCW,IAAI,CAACC,KAAK,CAACb,OAAO,CAAC;EACnBH,kBAAkB,CAACiB,GAAG,CAACd,OAAO,EAAEY,IAAI,CAAC;AACzC;AACA,SAASG,yBAAyBA,CAACC,OAAO,EAAE;EACxC,MAAMf,OAAO,GAAG;IACZC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,CAAC,CAAC;IACTC,WAAW,EAAE;MACTC,WAAW,EAAE;QACTY,MAAM,EAAE,CAAC;MACb,CAAC;MACDN,YAAY,EAAE,CAAC;IACnB;EACJ,CAAC;EACD,MAAMC,IAAI,GAAG,IAAIhB,mBAAmB,CAACK,OAAO,CAAC;EAC7CW,IAAI,CAACC,KAAK,CAACG,OAAO,CAAC;EACnBnB,kBAAkB,CAACiB,GAAG,CAACE,OAAO,EAAEJ,IAAI,CAAC;AACzC;AAEA,SAASb,sBAAsB,EAAEgB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}