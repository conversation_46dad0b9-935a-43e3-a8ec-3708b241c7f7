import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const CustomCursor: React.FC = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [trails, setTrails] = useState<Array<{ x: number; y: number; id: number }>>([]);

  useEffect(() => {
    let trailId = 0;
    
    const updateMousePosition = (e: MouseEvent) => {
      const newPosition = { x: e.clientX, y: e.clientY };
      setMousePosition(newPosition);
      
      // Add trail
      setTrails(prev => {
        const newTrail = { ...newPosition, id: trailId++ };
        const updatedTrails = [newTrail, ...prev.slice(0, 8)];
        return updatedTrails;
      });
    };

    const handleMouseEnter = () => setIsHovering(true);
    const handleMouseLeave = () => setIsHovering(false);

    // Add event listeners to interactive elements
    const interactiveElements = document.querySelectorAll('a, button, [role="button"], input, textarea, select');
    
    interactiveElements.forEach(el => {
      el.addEventListener('mouseenter', handleMouseEnter);
      el.addEventListener('mouseleave', handleMouseLeave);
    });

    window.addEventListener('mousemove', updateMousePosition);

    return () => {
      window.removeEventListener('mousemove', updateMousePosition);
      interactiveElements.forEach(el => {
        el.removeEventListener('mouseenter', handleMouseEnter);
        el.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []);

  return (
    <>
      {/* Main cursor */}
      <motion.div
        className="fixed pointer-events-none z-[9999] mix-blend-mode-difference"
        animate={{
          x: mousePosition.x - 10,
          y: mousePosition.y - 10,
          scale: isHovering ? 1.5 : 1,
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 28,
          mass: 0.5,
        }}
      >
        <div className="w-5 h-5 border-2 border-cyan-400 rounded-full relative">
          <div className="absolute inset-0 border border-cyan-400 rounded-full animate-ping opacity-75"></div>
          <div className="absolute inset-1 bg-cyan-400 rounded-full opacity-50"></div>
        </div>
      </motion.div>

      {/* Cursor trails */}
      {trails.map((trail, index) => (
        <motion.div
          key={trail.id}
          className="fixed pointer-events-none z-[9998]"
          initial={{
            x: trail.x - 2,
            y: trail.y - 2,
            opacity: 0.8,
            scale: 1,
          }}
          animate={{
            opacity: 0,
            scale: 0.5,
          }}
          transition={{
            duration: 0.8,
            ease: "easeOut",
          }}
        >
          <div 
            className="w-1 h-1 bg-cyan-400 rounded-full"
            style={{
              opacity: Math.max(0, 1 - index * 0.15),
              transform: `scale(${Math.max(0.3, 1 - index * 0.1)})`,
            }}
          />
        </motion.div>
      ))}

      {/* Hover effect */}
      {isHovering && (
        <motion.div
          className="fixed pointer-events-none z-[9997]"
          animate={{
            x: mousePosition.x - 25,
            y: mousePosition.y - 25,
          }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
          }}
        >
          <div className="w-12 h-12 border border-cyan-400/30 rounded-full animate-pulse">
            <div className="w-full h-full border border-cyan-400/20 rounded-full animate-ping"></div>
          </div>
        </motion.div>
      )}
    </>
  );
};

export default CustomCursor;
