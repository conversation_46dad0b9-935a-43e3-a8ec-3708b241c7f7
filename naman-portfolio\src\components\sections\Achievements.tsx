import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Medal, Award, Users, Calendar, Building } from 'lucide-react';
import { achievements } from '../../data/portfolio';

const Achievements: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState<string>('All');

  const categories = [
    { name: 'All', icon: Trophy },
    { name: 'Hackathon', icon: Trophy },
    { name: 'Competition', icon: Medal },
    { name: 'Award', icon: Award },
    { name: 'Recognition', icon: Users },
  ];

  const filteredAchievements = activeFilter === 'All' 
    ? achievements 
    : achievements.filter(achievement => achievement.category === activeFilter);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Hackathon': return Trophy;
      case 'Competition': return Medal;
      case 'Award': return Award;
      case 'Recognition': return Users;
      default: return Trophy;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Hackathon': return 'text-accent-400 bg-accent-500/10 border-accent-500/20';
      case 'Competition': return 'text-primary-400 bg-primary-500/10 border-primary-500/20';
      case 'Award': return 'text-secondary-400 bg-secondary-500/10 border-secondary-500/20';
      case 'Recognition': return 'text-purple-400 bg-purple-500/10 border-purple-500/20';
      default: return 'text-dark-400 bg-dark-500/10 border-dark-500/20';
    }
  };

  const getPositionColor = (position?: string) => {
    if (!position) return '';
    if (position.includes('1st') || position.includes('Winner') || position.includes('Top 10')) {
      return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';
    }
    if (position.includes('2nd') || position.includes('Runner')) {
      return 'text-gray-300 bg-gray-500/10 border-gray-500/20';
    }
    if (position.includes('3rd') || position.includes('Consolation')) {
      return 'text-orange-400 bg-orange-500/10 border-orange-500/20';
    }
    return 'text-blue-400 bg-blue-500/10 border-blue-500/20';
  };

  return (
    <section id="achievements" className="py-20 bg-dark-900">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold gradient-text mb-4">
            Achievements & Awards
          </h2>
          <p className="text-xl text-dark-300 max-w-3xl mx-auto">
            Recognition for innovation, leadership, and technical excellence in competitions and organizations
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.name}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: 0.1 * index }}
              onClick={() => setActiveFilter(category.name)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                activeFilter === category.name
                  ? 'bg-primary-600 text-white shadow-lg scale-105'
                  : 'bg-dark-700 text-dark-300 hover:bg-dark-600 hover:text-white'
              }`}
            >
              <category.icon size={16} />
              <span className="text-sm">{category.name}</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Achievements Grid */}
        <motion.div
          layout
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
        >
          {filteredAchievements.map((achievement, index) => {
            const CategoryIcon = getCategoryIcon(achievement.category);
            
            return (
              <motion.div
                key={achievement.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                className="bg-dark-800/50 rounded-lg border border-dark-700 hover:border-primary-500/50 transition-all duration-300 card-hover group overflow-hidden"
              >
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg ${getCategoryColor(achievement.category)}`}>
                      <CategoryIcon size={24} />
                    </div>
                    
                    {achievement.position && (
                      <div className={`px-3 py-1 rounded-full text-xs font-bold border ${getPositionColor(achievement.position)}`}>
                        {achievement.position}
                      </div>
                    )}
                  </div>

                  {/* Title */}
                  <h3 className="text-lg font-bold text-white mb-2 group-hover:text-primary-400 transition-colors duration-300">
                    {achievement.title}
                  </h3>

                  {/* Description */}
                  <p className="text-dark-300 text-sm leading-relaxed mb-4">
                    {achievement.description}
                  </p>

                  {/* Details */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-dark-400">
                      <Calendar size={14} />
                      <span>{achievement.date}</span>
                    </div>
                    
                    {achievement.organization && (
                      <div className="flex items-center gap-2 text-sm text-dark-400">
                        <Building size={14} />
                        <span>{achievement.organization}</span>
                      </div>
                    )}
                  </div>

                  {/* Category Badge */}
                  <div className="mt-4 pt-4 border-t border-dark-600">
                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getCategoryColor(achievement.category)}`}>
                      {achievement.category}
                    </span>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Achievements Summary */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid md:grid-cols-4 gap-6"
        >
          <div className="text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700">
            <div className="text-3xl font-bold gradient-text mb-2">
              {achievements.length}
            </div>
            <div className="text-dark-300 text-sm">Total Achievements</div>
          </div>
          
          <div className="text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700">
            <div className="text-3xl font-bold gradient-text mb-2">
              {achievements.filter(a => a.category === 'Hackathon').length}
            </div>
            <div className="text-dark-300 text-sm">Hackathon Wins</div>
          </div>
          
          <div className="text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700">
            <div className="text-3xl font-bold gradient-text mb-2">
              {achievements.filter(a => a.category === 'Recognition').length}
            </div>
            <div className="text-dark-300 text-sm">Leadership Roles</div>
          </div>
          
          <div className="text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700">
            <div className="text-3xl font-bold gradient-text mb-2">
              {achievements.filter(a => a.position && (a.position.includes('Top') || a.position.includes('Winner'))).length}
            </div>
            <div className="text-dark-300 text-sm">Top Positions</div>
          </div>
        </motion.div>

        {/* Notable Highlights */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 bg-gradient-to-r from-primary-500/10 via-secondary-500/10 to-accent-500/10 rounded-lg p-8 border border-primary-500/20"
        >
          <h3 className="text-2xl font-bold gradient-text text-center mb-6">
            Notable Highlights
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="text-center">
              <div className="text-4xl mb-2">🏆</div>
              <h4 className="text-lg font-semibold text-white mb-2">Smart India Hackathon</h4>
              <p className="text-dark-300 text-sm">
                Consistent performer with Top 10 (2022) and Consolation Prize (2024) in India's largest hackathon
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-2">👨‍💼</div>
              <h4 className="text-lg font-semibold text-white mb-2">Student Leadership</h4>
              <p className="text-dark-300 text-sm">
                Multiple leadership roles including Student Coordinator, Discipline Committee Head, and Welfare Member
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Achievements;
