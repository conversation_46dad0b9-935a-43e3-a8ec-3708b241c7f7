{"ast": null, "code": "import { transformProps, getDefaultValueType } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nclass SVGVisualElement extends DOMVisualElement {\n  constructor() {\n    super(...arguments);\n    this.type = \"svg\";\n    this.isSVGTag = false;\n    this.measureInstanceViewportBox = createBox;\n  }\n  getBaseTargetFromProps(props, key) {\n    return props[key];\n  }\n  readValueFromInstance(instance, key) {\n    if (transformProps.has(key)) {\n      const defaultType = getDefaultValueType(key);\n      return defaultType ? defaultType.default || 0 : 0;\n    }\n    key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n    return instance.getAttribute(key);\n  }\n  scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n  }\n  build(renderState, latestValues, props) {\n    buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate, props.style);\n  }\n  renderInstance(instance, renderState, styleProp, projection) {\n    renderSVG(instance, renderState, styleProp, projection);\n  }\n  mount(instance) {\n    this.isSVGTag = isSVGTag(instance.tagName);\n    super.mount(instance);\n  }\n}\nexport { SVGVisualElement };", "map": {"version": 3, "names": ["transformProps", "getDefaultValueType", "createBox", "DOMVisualElement", "camelToDash", "buildSVGAttrs", "camelCaseAttributes", "isSVGTag", "renderSVG", "scrapeMotionValuesFromProps", "SVGVisualElement", "constructor", "arguments", "type", "measureInstanceViewportBox", "getBaseTargetFromProps", "props", "key", "readValueFromInstance", "instance", "has", "defaultType", "default", "getAttribute", "prevProps", "visualElement", "build", "renderState", "latestValues", "transformTemplate", "style", "renderInstance", "styleProp", "projection", "mount", "tagName"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs"], "sourcesContent": ["import { transformProps, getDefaultValueType } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n        this.measureInstanceViewportBox = createBox;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n    build(renderState, latestValues, props) {\n        buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate, props.style);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,mBAAmB,QAAQ,YAAY;AAChE,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,MAAMC,gBAAgB,SAASP,gBAAgB,CAAC;EAC5CQ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACN,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACO,0BAA0B,GAAGZ,SAAS;EAC/C;EACAa,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOD,KAAK,CAACC,GAAG,CAAC;EACrB;EACAC,qBAAqBA,CAACC,QAAQ,EAAEF,GAAG,EAAE;IACjC,IAAIjB,cAAc,CAACoB,GAAG,CAACH,GAAG,CAAC,EAAE;MACzB,MAAMI,WAAW,GAAGpB,mBAAmB,CAACgB,GAAG,CAAC;MAC5C,OAAOI,WAAW,GAAGA,WAAW,CAACC,OAAO,IAAI,CAAC,GAAG,CAAC;IACrD;IACAL,GAAG,GAAG,CAACX,mBAAmB,CAACc,GAAG,CAACH,GAAG,CAAC,GAAGb,WAAW,CAACa,GAAG,CAAC,GAAGA,GAAG;IAC5D,OAAOE,QAAQ,CAACI,YAAY,CAACN,GAAG,CAAC;EACrC;EACAR,2BAA2BA,CAACO,KAAK,EAAEQ,SAAS,EAAEC,aAAa,EAAE;IACzD,OAAOhB,2BAA2B,CAACO,KAAK,EAAEQ,SAAS,EAAEC,aAAa,CAAC;EACvE;EACAC,KAAKA,CAACC,WAAW,EAAEC,YAAY,EAAEZ,KAAK,EAAE;IACpCX,aAAa,CAACsB,WAAW,EAAEC,YAAY,EAAE,IAAI,CAACrB,QAAQ,EAAES,KAAK,CAACa,iBAAiB,EAAEb,KAAK,CAACc,KAAK,CAAC;EACjG;EACAC,cAAcA,CAACZ,QAAQ,EAAEQ,WAAW,EAAEK,SAAS,EAAEC,UAAU,EAAE;IACzDzB,SAAS,CAACW,QAAQ,EAAEQ,WAAW,EAAEK,SAAS,EAAEC,UAAU,CAAC;EAC3D;EACAC,KAAKA,CAACf,QAAQ,EAAE;IACZ,IAAI,CAACZ,QAAQ,GAAGA,QAAQ,CAACY,QAAQ,CAACgB,OAAO,CAAC;IAC1C,KAAK,CAACD,KAAK,CAACf,QAAQ,CAAC;EACzB;AACJ;AAEA,SAAST,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}