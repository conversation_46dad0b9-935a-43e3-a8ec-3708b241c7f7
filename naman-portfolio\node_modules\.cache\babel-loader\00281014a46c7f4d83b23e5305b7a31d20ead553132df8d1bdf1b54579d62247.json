{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Menu, X, Github, Linkedin, Mail, ExternalLink } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navItems = [{\n    name: 'About',\n    href: '#about'\n  }, {\n    name: 'Skills',\n    href: '#skills'\n  }, {\n    name: 'Experience',\n    href: '#experience'\n  }, {\n    name: 'Projects',\n    href: '#projects'\n  }, {\n    name: 'Education',\n    href: '#education'\n  }, {\n    name: 'Achievements',\n    href: '#achievements'\n  }, {\n    name: 'Contact',\n    href: '#contact'\n  }];\n  const socialLinks = [{\n    icon: Github,\n    href: 'https://github.com/naman2002',\n    label: 'GitHub'\n  }, {\n    icon: Linkedin,\n    href: 'https://linkedin.com/in/naman-nagi-92026521',\n    label: 'LinkedIn'\n  }, {\n    icon: ExternalLink,\n    href: 'https://technaman.tech',\n    label: 'Website'\n  }, {\n    icon: Mail,\n    href: 'mailto:<EMAIL>',\n    label: 'Email'\n  }];\n  const scrollToSection = href => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n    setIsMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(motion.header, {\n    initial: {\n      y: -100\n    },\n    animate: {\n      y: 0\n    },\n    className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${isScrolled ? 'glass shadow-2xl border-b border-white/10' : 'bg-transparent'}`,\n    children: /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            delay: 0.2\n          },\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.scrollTo({\n              top: 0,\n              behavior: 'smooth'\n            }),\n            className: \"text-2xl font-black gradient-text hover:scale-110 transition-all duration-300 tracking-tight\",\n            children: \"Naman Nagi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:block\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-10 flex items-baseline space-x-4\",\n            children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              initial: {\n                opacity: 0,\n                y: -20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1 * index\n              },\n              href: item.href,\n              onClick: e => {\n                e.preventDefault();\n                scrollToSection(item.href);\n              },\n              className: \"text-slate-300 hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm relative group\",\n              children: item.name\n            }, item.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-4\",\n          children: socialLinks.map((link, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n            initial: {\n              opacity: 0,\n              scale: 0\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.3 + 0.1 * index\n            },\n            href: link.href,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"text-dark-400 hover:text-primary-400 transition-colors duration-300 hover:scale-110 transform\",\n            \"aria-label\": link.label,\n            children: /*#__PURE__*/_jsxDEV(link.icon, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)\n          }, link.label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsMenuOpen(!isMenuOpen),\n            className: \"text-dark-300 hover:text-primary-400 transition-colors duration-300\",\n            \"aria-label\": \"Toggle menu\",\n            children: isMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: false,\n        animate: {\n          height: isMenuOpen ? 'auto' : 0,\n          opacity: isMenuOpen ? 1 : 0\n        },\n        transition: {\n          duration: 0.3\n        },\n        className: \"md:hidden overflow-hidden bg-dark-900/95 backdrop-blur-md rounded-lg mt-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 pt-2 pb-3 space-y-1\",\n          children: [navItems.map(item => /*#__PURE__*/_jsxDEV(\"a\", {\n            href: item.href,\n            onClick: e => {\n              e.preventDefault();\n              scrollToSection(item.href);\n            },\n            className: \"text-dark-300 hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-300 hover:bg-dark-800/50\",\n            children: item.name\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-6 pt-4 pb-2\",\n            children: socialLinks.map(link => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: link.href,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"text-dark-400 hover:text-primary-400 transition-colors duration-300\",\n              \"aria-label\": link.label,\n              children: /*#__PURE__*/_jsxDEV(link.icon, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)\n            }, link.label, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"wcf3U8/NDcncqNPQTEEGYFGEme8=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON>", "X", "<PERSON><PERSON><PERSON>", "Linkedin", "Mail", "ExternalLink", "jsxDEV", "_jsxDEV", "Header", "_s", "isMenuOpen", "setIsMenuOpen", "isScrolled", "setIsScrolled", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "navItems", "name", "href", "socialLinks", "icon", "label", "scrollToSection", "element", "document", "querySelector", "scrollIntoView", "behavior", "header", "initial", "y", "animate", "className", "children", "div", "opacity", "transition", "delay", "onClick", "scrollTo", "top", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "a", "e", "preventDefault", "link", "scale", "target", "rel", "size", "height", "duration", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/layout/Header.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Menu, X, Github, Linkedin, Mail, ExternalLink } from 'lucide-react';\n\nconst Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Education', href: '#education' },\n    { name: 'Achievements', href: '#achievements' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com/naman2002', label: 'GitHub' },\n    { icon: Linkedin, href: 'https://linkedin.com/in/naman-nagi-92026521', label: 'LinkedIn' },\n    { icon: ExternalLink, href: 'https://technaman.tech', label: 'Website' },\n    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' },\n  ];\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n        isScrolled\n          ? 'glass shadow-2xl border-b border-white/10'\n          : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.2 }}\n            className=\"flex-shrink-0\"\n          >\n            <button\n              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n              className=\"text-2xl font-black gradient-text hover:scale-110 transition-all duration-300 tracking-tight\"\n            >\n              Naman Nagi\n            </button>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item, index) => (\n                <motion.a\n                  key={item.name}\n                  initial={{ opacity: 0, y: -20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.1 * index }}\n                  href={item.href}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    scrollToSection(item.href);\n                  }}\n                  className=\"text-slate-300 hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm relative group\"\n                >\n                  {item.name}\n                </motion.a>\n              ))}\n            </div>\n          </div>\n\n          {/* Social Links - Desktop */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {socialLinks.map((link, index) => (\n              <motion.a\n                key={link.label}\n                initial={{ opacity: 0, scale: 0 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.3 + 0.1 * index }}\n                href={link.href}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-dark-400 hover:text-primary-400 transition-colors duration-300 hover:scale-110 transform\"\n                aria-label={link.label}\n              >\n                <link.icon size={20} />\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-dark-300 hover:text-primary-400 transition-colors duration-300\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <motion.div\n          initial={false}\n          animate={{\n            height: isMenuOpen ? 'auto' : 0,\n            opacity: isMenuOpen ? 1 : 0,\n          }}\n          transition={{ duration: 0.3 }}\n          className=\"md:hidden overflow-hidden bg-dark-900/95 backdrop-blur-md rounded-lg mt-2\"\n        >\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navItems.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                onClick={(e) => {\n                  e.preventDefault();\n                  scrollToSection(item.href);\n                }}\n                className=\"text-dark-300 hover:text-primary-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-300 hover:bg-dark-800/50\"\n              >\n                {item.name}\n              </a>\n            ))}\n            \n            {/* Mobile Social Links */}\n            <div className=\"flex items-center justify-center space-x-6 pt-4 pb-2\">\n              {socialLinks.map((link) => (\n                <a\n                  key={link.label}\n                  href={link.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-dark-400 hover:text-primary-400 transition-colors duration-300\"\n                  aria-label={link.label}\n                >\n                  <link.icon size={20} />\n                </a>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n      </nav>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,CAAC,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBD,aAAa,CAACE,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAS,CAAC,EACjC;IAAED,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAU,CAAC,EACnC;IAAED,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAc,CAAC,EAC3C;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACvC;IAAED,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa,CAAC,EACzC;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAC/C;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,CACtC;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAErB,MAAM;IAAEmB,IAAI,EAAE,8BAA8B;IAAEG,KAAK,EAAE;EAAS,CAAC,EACvE;IAAED,IAAI,EAAEpB,QAAQ;IAAEkB,IAAI,EAAE,6CAA6C;IAAEG,KAAK,EAAE;EAAW,CAAC,EAC1F;IAAED,IAAI,EAAElB,YAAY;IAAEgB,IAAI,EAAE,wBAAwB;IAAEG,KAAK,EAAE;EAAU,CAAC,EACxE;IAAED,IAAI,EAAEnB,IAAI;IAAEiB,IAAI,EAAE,gCAAgC;IAAEG,KAAK,EAAE;EAAQ,CAAC,CACvE;EAED,MAAMC,eAAe,GAAIJ,IAAY,IAAK;IACxC,MAAMK,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAACP,IAAI,CAAC;IAC5C,IAAIK,OAAO,EAAE;MACXA,OAAO,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChD;IACAnB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACEJ,OAAA,CAACR,MAAM,CAACgC,MAAM;IACZC,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,SAAS,EAAE,+DACTvB,UAAU,GACN,2CAA2C,GAC3C,gBAAgB,EACnB;IAAAwB,QAAA,eAEH7B,OAAA;MAAK4B,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD7B,OAAA;QAAK4B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErD7B,OAAA,CAACR,MAAM,CAACsC,GAAG;UACTL,OAAO,EAAE;YAAEM,OAAO,EAAE;UAAE,CAAE;UACxBJ,OAAO,EAAE;YAAEI,OAAO,EAAE;UAAE,CAAE;UACxBC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC3BL,SAAS,EAAC,eAAe;UAAAC,QAAA,eAEzB7B,OAAA;YACEkC,OAAO,EAAEA,CAAA,KAAM1B,MAAM,CAAC2B,QAAQ,CAAC;cAAEC,GAAG,EAAE,CAAC;cAAEb,QAAQ,EAAE;YAAS,CAAC,CAAE;YAC/DK,SAAS,EAAC,8FAA8F;YAAAC,QAAA,EACzG;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGbxC,OAAA;UAAK4B,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B7B,OAAA;YAAK4B,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EACjDjB,QAAQ,CAAC6B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxB3C,OAAA,CAACR,MAAM,CAACoD,CAAC;cAEPnB,OAAO,EAAE;gBAAEM,OAAO,EAAE,CAAC;gBAAEL,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCC,OAAO,EAAE;gBAAEI,OAAO,EAAE,CAAC;gBAAEL,CAAC,EAAE;cAAE,CAAE;cAC9BM,UAAU,EAAE;gBAAEC,KAAK,EAAE,GAAG,GAAGU;cAAM,CAAE;cACnC7B,IAAI,EAAE4B,IAAI,CAAC5B,IAAK;cAChBoB,OAAO,EAAGW,CAAC,IAAK;gBACdA,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClB5B,eAAe,CAACwB,IAAI,CAAC5B,IAAI,CAAC;cAC5B,CAAE;cACFc,SAAS,EAAC,mKAAmK;cAAAC,QAAA,EAE5Ka,IAAI,CAAC7B;YAAI,GAXL6B,IAAI,CAAC7B,IAAI;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYN,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxC,OAAA;UAAK4B,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDd,WAAW,CAAC0B,GAAG,CAAC,CAACM,IAAI,EAAEJ,KAAK,kBAC3B3C,OAAA,CAACR,MAAM,CAACoD,CAAC;YAEPnB,OAAO,EAAE;cAAEM,OAAO,EAAE,CAAC;cAAEiB,KAAK,EAAE;YAAE,CAAE;YAClCrB,OAAO,EAAE;cAAEI,OAAO,EAAE,CAAC;cAAEiB,KAAK,EAAE;YAAE,CAAE;YAClChB,UAAU,EAAE;cAAEC,KAAK,EAAE,GAAG,GAAG,GAAG,GAAGU;YAAM,CAAE;YACzC7B,IAAI,EAAEiC,IAAI,CAACjC,IAAK;YAChBmC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBtB,SAAS,EAAC,+FAA+F;YACzG,cAAYmB,IAAI,CAAC9B,KAAM;YAAAY,QAAA,eAEvB7B,OAAA,CAAC+C,IAAI,CAAC/B,IAAI;cAACmC,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC,GAVlBO,IAAI,CAAC9B,KAAK;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWP,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxC,OAAA;UAAK4B,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxB7B,OAAA;YACEkC,OAAO,EAAEA,CAAA,KAAM9B,aAAa,CAAC,CAACD,UAAU,CAAE;YAC1CyB,SAAS,EAAC,qEAAqE;YAC/E,cAAW,aAAa;YAAAC,QAAA,EAEvB1B,UAAU,gBAAGH,OAAA,CAACN,CAAC;cAACyD,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxC,OAAA,CAACP,IAAI;cAAC0D,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxC,OAAA,CAACR,MAAM,CAACsC,GAAG;QACTL,OAAO,EAAE,KAAM;QACfE,OAAO,EAAE;UACPyB,MAAM,EAAEjD,UAAU,GAAG,MAAM,GAAG,CAAC;UAC/B4B,OAAO,EAAE5B,UAAU,GAAG,CAAC,GAAG;QAC5B,CAAE;QACF6B,UAAU,EAAE;UAAEqB,QAAQ,EAAE;QAAI,CAAE;QAC9BzB,SAAS,EAAC,2EAA2E;QAAAC,QAAA,eAErF7B,OAAA;UAAK4B,SAAS,EAAC,0BAA0B;UAAAC,QAAA,GACtCjB,QAAQ,CAAC6B,GAAG,CAAEC,IAAI,iBACjB1C,OAAA;YAEEc,IAAI,EAAE4B,IAAI,CAAC5B,IAAK;YAChBoB,OAAO,EAAGW,CAAC,IAAK;cACdA,CAAC,CAACC,cAAc,CAAC,CAAC;cAClB5B,eAAe,CAACwB,IAAI,CAAC5B,IAAI,CAAC;YAC5B,CAAE;YACFc,SAAS,EAAC,2IAA2I;YAAAC,QAAA,EAEpJa,IAAI,CAAC7B;UAAI,GARL6B,IAAI,CAAC7B,IAAI;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASb,CACJ,CAAC,eAGFxC,OAAA;YAAK4B,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAClEd,WAAW,CAAC0B,GAAG,CAAEM,IAAI,iBACpB/C,OAAA;cAEEc,IAAI,EAAEiC,IAAI,CAACjC,IAAK;cAChBmC,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBtB,SAAS,EAAC,qEAAqE;cAC/E,cAAYmB,IAAI,CAAC9B,KAAM;cAAAY,QAAA,eAEvB7B,OAAA,CAAC+C,IAAI,CAAC/B,IAAI;gBAACmC,IAAI,EAAE;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAPlBO,IAAI,CAAC9B,KAAK;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQd,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACtC,EAAA,CAnKID,MAAgB;AAAAqD,EAAA,GAAhBrD,MAAgB;AAqKtB,eAAeA,MAAM;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}