{"ast": null, "code": "const ScrollOffset = {\n  Enter: [[0, 1], [1, 1]],\n  Exit: [[0, 0], [1, 0]],\n  Any: [[1, 0], [0, 1]],\n  All: [[0, 0], [1, 1]]\n};\nexport { ScrollOffset };", "map": {"version": 3, "names": ["ScrollOffset", "Enter", "Exit", "Any", "All"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs"], "sourcesContent": ["const ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\nexport { ScrollOffset };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,KAAK,EAAE,CACH,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,CAAC,EAAE,CAAC,CAAC,CACT;EACDC,IAAI,EAAE,CACF,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,CAAC,EAAE,CAAC,CAAC,CACT;EACDC,GAAG,EAAE,CACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,CAAC,EAAE,CAAC,CAAC,CACT;EACDC,GAAG,EAAE,CACD,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,CAAC,EAAE,CAAC,CAAC;AAEd,CAAC;AAED,SAASJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}