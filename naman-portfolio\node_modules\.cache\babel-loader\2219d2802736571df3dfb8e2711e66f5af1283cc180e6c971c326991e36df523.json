{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\sections\\\\About.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Brain, Code, Zap, Target, Users, Award } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  const highlights = [{\n    icon: Brain,\n    title: \"AI/ML Expertise\",\n    description: \"Specialized in Generative AI, LLMs, Computer Vision, and NLP with hands-on experience in Stable Diffusion, LoRA, and ControlNet.\"\n  }, {\n    icon: Code,\n    title: \"Full-Stack Development\",\n    description: \"Proficient in building scalable applications with FastAPI, React, and modern web technologies, with strong backend optimization skills.\"\n  }, {\n    icon: Zap,\n    title: \"Automation & Efficiency\",\n    description: \"Expert in creating automated systems for HR processes, social media management, and business workflow optimization.\"\n  }, {\n    icon: Target,\n    title: \"Problem Solver\",\n    description: \"Proven track record in hackathons and competitions, with multiple wins and recognitions for innovative solutions.\"\n  }, {\n    icon: Users,\n    title: \"Team Leadership\",\n    description: \"Experience in leading teams and coordinating projects, with roles in student organizations and committee leadership.\"\n  }, {\n    icon: Award,\n    title: \"Continuous Learning\",\n    description: \"Multiple certifications from Google, NVIDIA, AWS, and other leading tech companies, staying updated with latest technologies.\"\n  }];\n  const stats = [{\n    number: \"2+\",\n    label: \"Years Experience\"\n  }, {\n    number: \"15+\",\n    label: \"Projects Completed\"\n  }, {\n    number: \"10+\",\n    label: \"Certifications\"\n  }, {\n    number: \"5+\",\n    label: \"Hackathon Wins\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"py-24 bg-gradient-to-b from-slate-900/50 to-slate-800/30 relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-20 left-10 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-20 right-10 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl sm:text-5xl font-bold gradient-text mb-4\",\n          children: \"About Me\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-dark-300 max-w-3xl mx-auto\",\n          children: \"Passionate about leveraging AI and technology to create sustainable solutions that make a real-world impact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-2 gap-12 items-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -30\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-semibold text-white mb-4\",\n            children: \"My Journey in AI & Technology\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4 text-dark-300 leading-relaxed\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"I'm a dynamic AI/ML engineer with over 2 years of experience building energy-efficient, automated, and scalable AI solutions. Currently pursuing my Bachelor's in Computer Science with specialization in AI/ML from Uttaranchal University.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"My expertise spans across machine learning model optimization, green architecture, and backend systems development. I've worked extensively with cutting-edge technologies like Stable Diffusion models, LoRA techniques, and large language models to create innovative solutions.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"At Rapid Innovation, I've built scalable FastAPI backends with real-time AI agent communications, optimized system throughput by 40%, and developed multi-agent systems for contextual conversations. My work focuses on sustainable impact in agriculture, environment monitoring, and smart infrastructure.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Beyond technical skills, I'm passionate about hackathons and competitions, having won multiple awards including Smart India Hackathon recognitions. I believe in continuous learning and have earned certifications from Google, NVIDIA, AWS, and other leading tech companies.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 30\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.2\n          },\n          className: \"grid grid-cols-2 gap-6\",\n          children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.1 * index\n            },\n            className: \"text-center p-6 bg-dark-700/50 rounded-lg border border-dark-600 hover:border-primary-500/50 transition-colors duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold gradient-text mb-2\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-dark-300 text-sm font-medium\",\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)]\n          }, stat.label, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.4\n        },\n        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: highlights.map((highlight, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.1 * index\n          },\n          className: \"p-6 bg-dark-700/30 rounded-lg border border-dark-600 hover:border-primary-500/50 transition-all duration-300 card-hover group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-primary-500/10 rounded-lg group-hover:bg-primary-500/20 transition-colors duration-300\",\n              children: /*#__PURE__*/_jsxDEV(highlight.icon, {\n                className: \"w-6 h-6 text-primary-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-white ml-3\",\n              children: highlight.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-dark-300 text-sm leading-relaxed\",\n            children: highlight.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, highlight.title, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "motion", "Brain", "Code", "Zap", "Target", "Users", "Award", "jsxDEV", "_jsxDEV", "About", "highlights", "icon", "title", "description", "stats", "number", "label", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "x", "delay", "map", "stat", "index", "scale", "highlight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/sections/About.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Brain, Code, Zap, Target, Users, Award } from 'lucide-react';\n\nconst About: React.FC = () => {\n  const highlights = [\n    {\n      icon: Brain,\n      title: \"AI/ML Expertise\",\n      description: \"Specialized in Generative AI, LLMs, Computer Vision, and NLP with hands-on experience in Stable Diffusion, LoRA, and ControlNet.\"\n    },\n    {\n      icon: Code,\n      title: \"Full-Stack Development\",\n      description: \"Proficient in building scalable applications with FastAPI, React, and modern web technologies, with strong backend optimization skills.\"\n    },\n    {\n      icon: Zap,\n      title: \"Automation & Efficiency\",\n      description: \"Expert in creating automated systems for HR processes, social media management, and business workflow optimization.\"\n    },\n    {\n      icon: Target,\n      title: \"Problem Solver\",\n      description: \"Proven track record in hackathons and competitions, with multiple wins and recognitions for innovative solutions.\"\n    },\n    {\n      icon: Users,\n      title: \"Team Leadership\",\n      description: \"Experience in leading teams and coordinating projects, with roles in student organizations and committee leadership.\"\n    },\n    {\n      icon: Award,\n      title: \"Continuous Learning\",\n      description: \"Multiple certifications from Google, NVIDIA, AWS, and other leading tech companies, staying updated with latest technologies.\"\n    }\n  ];\n\n  const stats = [\n    { number: \"2+\", label: \"Years Experience\" },\n    { number: \"15+\", label: \"Projects Completed\" },\n    { number: \"10+\", label: \"Certifications\" },\n    { number: \"5+\", label: \"Hackathon Wins\" }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-24 bg-gradient-to-b from-slate-900/50 to-slate-800/30 relative\">\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-4\">\n            About Me\n          </h2>\n          <p className=\"text-xl text-dark-300 max-w-3xl mx-auto\">\n            Passionate about leveraging AI and technology to create sustainable solutions \n            that make a real-world impact\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center mb-16\">\n          {/* Left Column - Story */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-6\"\n          >\n            <h3 className=\"text-2xl font-semibold text-white mb-4\">\n              My Journey in AI & Technology\n            </h3>\n            \n            <div className=\"space-y-4 text-dark-300 leading-relaxed\">\n              <p>\n                I'm a dynamic AI/ML engineer with over 2 years of experience building energy-efficient, \n                automated, and scalable AI solutions. Currently pursuing my Bachelor's in Computer Science \n                with specialization in AI/ML from Uttaranchal University.\n              </p>\n              \n              <p>\n                My expertise spans across machine learning model optimization, green architecture, \n                and backend systems development. I've worked extensively with cutting-edge technologies \n                like Stable Diffusion models, LoRA techniques, and large language models to create \n                innovative solutions.\n              </p>\n              \n              <p>\n                At Rapid Innovation, I've built scalable FastAPI backends with real-time AI agent \n                communications, optimized system throughput by 40%, and developed multi-agent systems \n                for contextual conversations. My work focuses on sustainable impact in agriculture, \n                environment monitoring, and smart infrastructure.\n              </p>\n              \n              <p>\n                Beyond technical skills, I'm passionate about hackathons and competitions, having won \n                multiple awards including Smart India Hackathon recognitions. I believe in continuous \n                learning and have earned certifications from Google, NVIDIA, AWS, and other leading \n                tech companies.\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Right Column - Stats */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"grid grid-cols-2 gap-6\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 0.1 * index }}\n                className=\"text-center p-6 bg-dark-700/50 rounded-lg border border-dark-600 hover:border-primary-500/50 transition-colors duration-300\"\n              >\n                <div className=\"text-3xl font-bold gradient-text mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-dark-300 text-sm font-medium\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n\n        {/* Highlights Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\"\n        >\n          {highlights.map((highlight, index) => (\n            <motion.div\n              key={highlight.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 * index }}\n              className=\"p-6 bg-dark-700/30 rounded-lg border border-dark-600 hover:border-primary-500/50 transition-all duration-300 card-hover group\"\n            >\n              <div className=\"flex items-center mb-4\">\n                <div className=\"p-3 bg-primary-500/10 rounded-lg group-hover:bg-primary-500/20 transition-colors duration-300\">\n                  <highlight.icon className=\"w-6 h-6 text-primary-400\" />\n                </div>\n                <h4 className=\"text-lg font-semibold text-white ml-3\">\n                  {highlight.title}\n                </h4>\n              </div>\n              <p className=\"text-dark-300 text-sm leading-relaxed\">\n                {highlight.description}\n              </p>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,UAAU,GAAG,CACjB;IACEC,IAAI,EAAEV,KAAK;IACXW,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAET,IAAI;IACVU,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAER,GAAG;IACTS,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEP,MAAM;IACZQ,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEN,KAAK;IACXO,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAEL,KAAK;IACXM,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAmB,CAAC,EAC3C;IAAED,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAC9C;IAAED,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAiB,CAAC,EAC1C;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAiB,CAAC,CAC1C;EAED,oBACER,OAAA;IAASS,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,mEAAmE;IAAAC,QAAA,gBAE/FX,OAAA;MAAKU,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CX,OAAA;QAAKU,SAAS,EAAC;MAAuE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7Ff,OAAA;QAAKU,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC,eAENf,OAAA;MAAKU,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBAEnEX,OAAA,CAACR,MAAM,CAACwB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BZ,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BX,OAAA;UAAIU,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLf,OAAA;UAAGU,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAGvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEbf,OAAA;QAAKU,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAE5DX,OAAA,CAACR,MAAM,CAACwB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCH,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCF,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BZ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAErBX,OAAA;YAAIU,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELf,OAAA;YAAKU,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBACtDX,OAAA;cAAAW,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJf,OAAA;cAAAW,QAAA,EAAG;YAKH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJf,OAAA;cAAAW,QAAA,EAAG;YAKH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJf,OAAA;cAAAW,QAAA,EAAG;YAKH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbf,OAAA,CAACR,MAAM,CAACwB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BH,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAClCF,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAC1Cd,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAEjCL,KAAK,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB3B,OAAA,CAACR,MAAM,CAACwB,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAI,CAAE;YACpCR,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEU,KAAK,EAAE;YAAE,CAAE;YACtCP,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE,GAAG,GAAGG;YAAM,CAAE;YAClDjB,SAAS,EAAC,6HAA6H;YAAAC,QAAA,gBAEvIX,OAAA;cAAKU,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDe,IAAI,CAACnB;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNf,OAAA;cAAKU,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC/Ce,IAAI,CAAClB;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,GAXDW,IAAI,CAAClB,KAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNf,OAAA,CAACR,MAAM,CAACwB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEE,KAAK,EAAE;QAAI,CAAE;QAC1Cd,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAEnDT,UAAU,CAACuB,GAAG,CAAC,CAACI,SAAS,EAAEF,KAAK,kBAC/B3B,OAAA,CAACR,MAAM,CAACwB,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE,GAAG,GAAGG;UAAM,CAAE;UAClDjB,SAAS,EAAC,+HAA+H;UAAAC,QAAA,gBAEzIX,OAAA;YAAKU,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCX,OAAA;cAAKU,SAAS,EAAC,+FAA+F;cAAAC,QAAA,eAC5GX,OAAA,CAAC6B,SAAS,CAAC1B,IAAI;gBAACO,SAAS,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNf,OAAA;cAAIU,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAClDkB,SAAS,CAACzB;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNf,OAAA;YAAGU,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACjDkB,SAAS,CAACxB;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA,GAhBCc,SAAS,CAACzB,KAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBV,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACe,EAAA,GAtKI7B,KAAe;AAwKrB,eAAeA,KAAK;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}