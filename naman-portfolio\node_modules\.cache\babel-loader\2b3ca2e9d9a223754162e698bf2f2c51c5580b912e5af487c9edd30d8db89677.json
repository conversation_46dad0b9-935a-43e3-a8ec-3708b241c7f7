{"ast": null, "code": "import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({\n  protectedKeys,\n  needsAnimating\n}, key) {\n  const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n  needsAnimating[key] = false;\n  return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition, {\n  delay = 0,\n  transitionOverride,\n  type\n} = {}) {\n  let {\n    transition = visualElement.getDefaultTransition(),\n    transitionEnd,\n    ...target\n  } = targetAndTransition;\n  if (transitionOverride) transition = transitionOverride;\n  const animations = [];\n  const animationTypeState = type && visualElement.animationState && visualElement.animationState.getState()[type];\n  for (const key in target) {\n    const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n    const valueTarget = target[key];\n    if (valueTarget === undefined || animationTypeState && shouldBlockAnimation(animationTypeState, key)) {\n      continue;\n    }\n    const valueTransition = {\n      delay,\n      ...getValueTransition(transition || {}, key)\n    };\n    /**\n     * If the value is already at the defined target, skip the animation.\n     */\n    const currentValue = value.get();\n    if (currentValue !== undefined && !value.isAnimating && !Array.isArray(valueTarget) && valueTarget === currentValue && !valueTransition.velocity) {\n      continue;\n    }\n    /**\n     * If this is the first time a value is being animated, check\n     * to see if we're handling off from an existing animation.\n     */\n    let isHandoff = false;\n    if (window.MotionHandoffAnimation) {\n      const appearId = getOptimisedAppearId(visualElement);\n      if (appearId) {\n        const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n        if (startTime !== null) {\n          valueTransition.startTime = startTime;\n          isHandoff = true;\n        }\n      }\n    }\n    addValueToWillChange(visualElement, key);\n    value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key) ? {\n      type: false\n    } : valueTransition, visualElement, isHandoff));\n    const animation = value.animation;\n    if (animation) {\n      animations.push(animation);\n    }\n  }\n  if (transitionEnd) {\n    Promise.all(animations).then(() => {\n      frame.update(() => {\n        transitionEnd && setTarget(visualElement, transitionEnd);\n      });\n    });\n  }\n  return animations;\n}\nexport { animateTarget };", "map": {"version": 3, "names": ["getValueTransition", "frame", "positional<PERSON>eys", "<PERSON><PERSON><PERSON><PERSON>", "addValueToWillChange", "getOptimisedAppearId", "animateMotionValue", "shouldBlockAnimation", "protected<PERSON><PERSON>s", "needsAnimating", "key", "shouldBlock", "hasOwnProperty", "animate<PERSON>arget", "visualElement", "targetAndTransition", "delay", "transitionOverride", "type", "transition", "getDefaultTransition", "transitionEnd", "target", "animations", "animationTypeState", "animationState", "getState", "value", "getValue", "latestValues", "valueTarget", "undefined", "valueTransition", "currentValue", "get", "isAnimating", "Array", "isArray", "velocity", "<PERSON><PERSON><PERSON><PERSON>", "window", "MotionHandoffAnimation", "appearId", "startTime", "start", "shouldReduceMotion", "has", "animation", "push", "Promise", "all", "then", "update"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs"], "sourcesContent": ["import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = targetAndTransition;\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n        const valueTarget = target[key];\n        if (valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If the value is already at the defined target, skip the animation.\n         */\n        const currentValue = value.get();\n        if (currentValue !== undefined &&\n            !value.isAnimating &&\n            !Array.isArray(valueTarget) &&\n            valueTarget === currentValue &&\n            !valueTransition.velocity) {\n            continue;\n        }\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        let isHandoff = false;\n        if (window.MotionHandoffAnimation) {\n            const appearId = getOptimisedAppearId(visualElement);\n            if (appearId) {\n                const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n                if (startTime !== null) {\n                    valueTransition.startTime = startTime;\n                    isHandoff = true;\n                }\n            }\n        }\n        addValueToWillChange(visualElement, key);\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key)\n            ? { type: false }\n            : valueTransition, visualElement, isHandoff));\n        const animation = value.animation;\n        if (animation) {\n            animations.push(animation);\n        }\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            frame.update(() => {\n                transitionEnd && setTarget(visualElement, transitionEnd);\n            });\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,KAAK,EAAEC,cAAc,QAAQ,YAAY;AACtE,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,oBAAoB,QAAQ,iDAAiD;AACtF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,kBAAkB,QAAQ,oBAAoB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC;EAAEC,aAAa;EAAEC;AAAe,CAAC,EAAEC,GAAG,EAAE;EAClE,MAAMC,WAAW,GAAGH,aAAa,CAACI,cAAc,CAACF,GAAG,CAAC,IAAID,cAAc,CAACC,GAAG,CAAC,KAAK,IAAI;EACrFD,cAAc,CAACC,GAAG,CAAC,GAAG,KAAK;EAC3B,OAAOC,WAAW;AACtB;AACA,SAASE,aAAaA,CAACC,aAAa,EAAEC,mBAAmB,EAAE;EAAEC,KAAK,GAAG,CAAC;EAAEC,kBAAkB;EAAEC;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EACrG,IAAI;IAAEC,UAAU,GAAGL,aAAa,CAACM,oBAAoB,CAAC,CAAC;IAAEC,aAAa;IAAE,GAAGC;EAAO,CAAC,GAAGP,mBAAmB;EACzG,IAAIE,kBAAkB,EAClBE,UAAU,GAAGF,kBAAkB;EACnC,MAAMM,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAGN,IAAI,IAC3BJ,aAAa,CAACW,cAAc,IAC5BX,aAAa,CAACW,cAAc,CAACC,QAAQ,CAAC,CAAC,CAACR,IAAI,CAAC;EACjD,KAAK,MAAMR,GAAG,IAAIY,MAAM,EAAE;IACtB,MAAMK,KAAK,GAAGb,aAAa,CAACc,QAAQ,CAAClB,GAAG,EAAEI,aAAa,CAACe,YAAY,CAACnB,GAAG,CAAC,IAAI,IAAI,CAAC;IAClF,MAAMoB,WAAW,GAAGR,MAAM,CAACZ,GAAG,CAAC;IAC/B,IAAIoB,WAAW,KAAKC,SAAS,IACxBP,kBAAkB,IACfjB,oBAAoB,CAACiB,kBAAkB,EAAEd,GAAG,CAAE,EAAE;MACpD;IACJ;IACA,MAAMsB,eAAe,GAAG;MACpBhB,KAAK;MACL,GAAGhB,kBAAkB,CAACmB,UAAU,IAAI,CAAC,CAAC,EAAET,GAAG;IAC/C,CAAC;IACD;AACR;AACA;IACQ,MAAMuB,YAAY,GAAGN,KAAK,CAACO,GAAG,CAAC,CAAC;IAChC,IAAID,YAAY,KAAKF,SAAS,IAC1B,CAACJ,KAAK,CAACQ,WAAW,IAClB,CAACC,KAAK,CAACC,OAAO,CAACP,WAAW,CAAC,IAC3BA,WAAW,KAAKG,YAAY,IAC5B,CAACD,eAAe,CAACM,QAAQ,EAAE;MAC3B;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,MAAM,CAACC,sBAAsB,EAAE;MAC/B,MAAMC,QAAQ,GAAGrC,oBAAoB,CAACS,aAAa,CAAC;MACpD,IAAI4B,QAAQ,EAAE;QACV,MAAMC,SAAS,GAAGH,MAAM,CAACC,sBAAsB,CAACC,QAAQ,EAAEhC,GAAG,EAAET,KAAK,CAAC;QACrE,IAAI0C,SAAS,KAAK,IAAI,EAAE;UACpBX,eAAe,CAACW,SAAS,GAAGA,SAAS;UACrCJ,SAAS,GAAG,IAAI;QACpB;MACJ;IACJ;IACAnC,oBAAoB,CAACU,aAAa,EAAEJ,GAAG,CAAC;IACxCiB,KAAK,CAACiB,KAAK,CAACtC,kBAAkB,CAACI,GAAG,EAAEiB,KAAK,EAAEG,WAAW,EAAEhB,aAAa,CAAC+B,kBAAkB,IAAI3C,cAAc,CAAC4C,GAAG,CAACpC,GAAG,CAAC,GAC7G;MAAEQ,IAAI,EAAE;IAAM,CAAC,GACfc,eAAe,EAAElB,aAAa,EAAEyB,SAAS,CAAC,CAAC;IACjD,MAAMQ,SAAS,GAAGpB,KAAK,CAACoB,SAAS;IACjC,IAAIA,SAAS,EAAE;MACXxB,UAAU,CAACyB,IAAI,CAACD,SAAS,CAAC;IAC9B;EACJ;EACA,IAAI1B,aAAa,EAAE;IACf4B,OAAO,CAACC,GAAG,CAAC3B,UAAU,CAAC,CAAC4B,IAAI,CAAC,MAAM;MAC/BlD,KAAK,CAACmD,MAAM,CAAC,MAAM;QACf/B,aAAa,IAAIlB,SAAS,CAACW,aAAa,EAAEO,aAAa,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAOE,UAAU;AACrB;AAEA,SAASV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}