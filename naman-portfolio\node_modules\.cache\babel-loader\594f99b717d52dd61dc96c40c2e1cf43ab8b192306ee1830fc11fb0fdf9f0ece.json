{"ast": null, "code": "import { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\nconst numVariantProps = variantProps.length;\nfunction getVariantContext(visualElement) {\n  if (!visualElement) return undefined;\n  if (!visualElement.isControllingVariants) {\n    const context = visualElement.parent ? getVariantContext(visualElement.parent) || {} : {};\n    if (visualElement.props.initial !== undefined) {\n      context.initial = visualElement.props.initial;\n    }\n    return context;\n  }\n  const context = {};\n  for (let i = 0; i < numVariantProps; i++) {\n    const name = variantProps[i];\n    const prop = visualElement.props[name];\n    if (isVariantLabel(prop) || prop === false) {\n      context[name] = prop;\n    }\n  }\n  return context;\n}\nexport { getVariantContext };", "map": {"version": 3, "names": ["isVariantLabel", "variantProps", "numVariantProps", "length", "getVariantContext", "visualElement", "undefined", "isControllingVariants", "context", "parent", "props", "initial", "i", "name", "prop"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs"], "sourcesContent": ["import { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nconst numVariantProps = variantProps.length;\nfunction getVariantContext(visualElement) {\n    if (!visualElement)\n        return undefined;\n    if (!visualElement.isControllingVariants) {\n        const context = visualElement.parent\n            ? getVariantContext(visualElement.parent) || {}\n            : {};\n        if (visualElement.props.initial !== undefined) {\n            context.initial = visualElement.props.initial;\n        }\n        return context;\n    }\n    const context = {};\n    for (let i = 0; i < numVariantProps; i++) {\n        const name = variantProps[i];\n        const prop = visualElement.props[name];\n        if (isVariantLabel(prop) || prop === false) {\n            context[name] = prop;\n        }\n    }\n    return context;\n}\n\nexport { getVariantContext };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,YAAY,QAAQ,qBAAqB;AAElD,MAAMC,eAAe,GAAGD,YAAY,CAACE,MAAM;AAC3C,SAASC,iBAAiBA,CAACC,aAAa,EAAE;EACtC,IAAI,CAACA,aAAa,EACd,OAAOC,SAAS;EACpB,IAAI,CAACD,aAAa,CAACE,qBAAqB,EAAE;IACtC,MAAMC,OAAO,GAAGH,aAAa,CAACI,MAAM,GAC9BL,iBAAiB,CAACC,aAAa,CAACI,MAAM,CAAC,IAAI,CAAC,CAAC,GAC7C,CAAC,CAAC;IACR,IAAIJ,aAAa,CAACK,KAAK,CAACC,OAAO,KAAKL,SAAS,EAAE;MAC3CE,OAAO,CAACG,OAAO,GAAGN,aAAa,CAACK,KAAK,CAACC,OAAO;IACjD;IACA,OAAOH,OAAO;EAClB;EACA,MAAMA,OAAO,GAAG,CAAC,CAAC;EAClB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,eAAe,EAAEU,CAAC,EAAE,EAAE;IACtC,MAAMC,IAAI,GAAGZ,YAAY,CAACW,CAAC,CAAC;IAC5B,MAAME,IAAI,GAAGT,aAAa,CAACK,KAAK,CAACG,IAAI,CAAC;IACtC,IAAIb,cAAc,CAACc,IAAI,CAAC,IAAIA,IAAI,KAAK,KAAK,EAAE;MACxCN,OAAO,CAACK,IAAI,CAAC,GAAGC,IAAI;IACxB;EACJ;EACA,OAAON,OAAO;AAClB;AAEA,SAASJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}