{"ast": null, "code": "import { frame, mixNumber, setDragLock, percent } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\nconst elementDragControls = new WeakMap();\nclass VisualElementDragControls {\n  constructor(visualElement) {\n    this.openDragLock = null;\n    this.isDragging = false;\n    this.currentDirection = null;\n    this.originPoint = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * The permitted boundaries of travel, in pixels.\n     */\n    this.constraints = false;\n    this.hasMutatedConstraints = false;\n    /**\n     * The per-axis resolved elastic values.\n     */\n    this.elastic = createBox();\n    /**\n     * The latest pointer event. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n     */\n    this.latestPointerEvent = null;\n    /**\n     * The latest pan info. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n     */\n    this.latestPanInfo = null;\n    this.visualElement = visualElement;\n  }\n  start(originEvent, {\n    snapToCursor = false,\n    distanceThreshold\n  } = {}) {\n    /**\n     * Don't start dragging if this component is exiting\n     */\n    const {\n      presenceContext\n    } = this.visualElement;\n    if (presenceContext && presenceContext.isPresent === false) return;\n    const onSessionStart = event => {\n      const {\n        dragSnapToOrigin\n      } = this.getProps();\n      // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n      // the component.\n      dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n      if (snapToCursor) {\n        this.snapToCursor(extractEventInfo(event).point);\n      }\n    };\n    const onStart = (event, info) => {\n      // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n      const {\n        drag,\n        dragPropagation,\n        onDragStart\n      } = this.getProps();\n      if (drag && !dragPropagation) {\n        if (this.openDragLock) this.openDragLock();\n        this.openDragLock = setDragLock(drag);\n        // If we don 't have the lock, don't start dragging\n        if (!this.openDragLock) return;\n      }\n      this.latestPointerEvent = event;\n      this.latestPanInfo = info;\n      this.isDragging = true;\n      this.currentDirection = null;\n      this.resolveConstraints();\n      if (this.visualElement.projection) {\n        this.visualElement.projection.isAnimationBlocked = true;\n        this.visualElement.projection.target = undefined;\n      }\n      /**\n       * Record gesture origin\n       */\n      eachAxis(axis => {\n        let current = this.getAxisMotionValue(axis).get() || 0;\n        /**\n         * If the MotionValue is a percentage value convert to px\n         */\n        if (percent.test(current)) {\n          const {\n            projection\n          } = this.visualElement;\n          if (projection && projection.layout) {\n            const measuredAxis = projection.layout.layoutBox[axis];\n            if (measuredAxis) {\n              const length = calcLength(measuredAxis);\n              current = length * (parseFloat(current) / 100);\n            }\n          }\n        }\n        this.originPoint[axis] = current;\n      });\n      // Fire onDragStart event\n      if (onDragStart) {\n        frame.postRender(() => onDragStart(event, info));\n      }\n      addValueToWillChange(this.visualElement, \"transform\");\n      const {\n        animationState\n      } = this.visualElement;\n      animationState && animationState.setActive(\"whileDrag\", true);\n    };\n    const onMove = (event, info) => {\n      this.latestPointerEvent = event;\n      this.latestPanInfo = info;\n      const {\n        dragPropagation,\n        dragDirectionLock,\n        onDirectionLock,\n        onDrag\n      } = this.getProps();\n      // If we didn't successfully receive the gesture lock, early return.\n      if (!dragPropagation && !this.openDragLock) return;\n      const {\n        offset\n      } = info;\n      // Attempt to detect drag direction if directionLock is true\n      if (dragDirectionLock && this.currentDirection === null) {\n        this.currentDirection = getCurrentDirection(offset);\n        // If we've successfully set a direction, notify listener\n        if (this.currentDirection !== null) {\n          onDirectionLock && onDirectionLock(this.currentDirection);\n        }\n        return;\n      }\n      // Update each point with the latest position\n      this.updateAxis(\"x\", info.point, offset);\n      this.updateAxis(\"y\", info.point, offset);\n      /**\n       * Ideally we would leave the renderer to fire naturally at the end of\n       * this frame but if the element is about to change layout as the result\n       * of a re-render we want to ensure the browser can read the latest\n       * bounding box to ensure the pointer and element don't fall out of sync.\n       */\n      this.visualElement.render();\n      /**\n       * This must fire after the render call as it might trigger a state\n       * change which itself might trigger a layout update.\n       */\n      onDrag && onDrag(event, info);\n    };\n    const onSessionEnd = (event, info) => {\n      this.latestPointerEvent = event;\n      this.latestPanInfo = info;\n      this.stop(event, info);\n      this.latestPointerEvent = null;\n      this.latestPanInfo = null;\n    };\n    const resumeAnimation = () => eachAxis(axis => this.getAnimationState(axis) === \"paused\" && this.getAxisMotionValue(axis).animation?.play());\n    const {\n      dragSnapToOrigin\n    } = this.getProps();\n    this.panSession = new PanSession(originEvent, {\n      onSessionStart,\n      onStart,\n      onMove,\n      onSessionEnd,\n      resumeAnimation\n    }, {\n      transformPagePoint: this.visualElement.getTransformPagePoint(),\n      dragSnapToOrigin,\n      distanceThreshold,\n      contextWindow: getContextWindow(this.visualElement)\n    });\n  }\n  /**\n   * @internal\n   */\n  stop(event, panInfo) {\n    const finalEvent = event || this.latestPointerEvent;\n    const finalPanInfo = panInfo || this.latestPanInfo;\n    const isDragging = this.isDragging;\n    this.cancel();\n    if (!isDragging || !finalPanInfo || !finalEvent) return;\n    const {\n      velocity\n    } = finalPanInfo;\n    this.startAnimation(velocity);\n    const {\n      onDragEnd\n    } = this.getProps();\n    if (onDragEnd) {\n      frame.postRender(() => onDragEnd(finalEvent, finalPanInfo));\n    }\n  }\n  /**\n   * @internal\n   */\n  cancel() {\n    this.isDragging = false;\n    const {\n      projection,\n      animationState\n    } = this.visualElement;\n    if (projection) {\n      projection.isAnimationBlocked = false;\n    }\n    this.panSession && this.panSession.end();\n    this.panSession = undefined;\n    const {\n      dragPropagation\n    } = this.getProps();\n    if (!dragPropagation && this.openDragLock) {\n      this.openDragLock();\n      this.openDragLock = null;\n    }\n    animationState && animationState.setActive(\"whileDrag\", false);\n  }\n  updateAxis(axis, _point, offset) {\n    const {\n      drag\n    } = this.getProps();\n    // If we're not dragging this axis, do an early return.\n    if (!offset || !shouldDrag(axis, drag, this.currentDirection)) return;\n    const axisValue = this.getAxisMotionValue(axis);\n    let next = this.originPoint[axis] + offset[axis];\n    // Apply constraints\n    if (this.constraints && this.constraints[axis]) {\n      next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n    }\n    axisValue.set(next);\n  }\n  resolveConstraints() {\n    const {\n      dragConstraints,\n      dragElastic\n    } = this.getProps();\n    const layout = this.visualElement.projection && !this.visualElement.projection.layout ? this.visualElement.projection.measure(false) : this.visualElement.projection?.layout;\n    const prevConstraints = this.constraints;\n    if (dragConstraints && isRefObject(dragConstraints)) {\n      if (!this.constraints) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    } else {\n      if (dragConstraints && layout) {\n        this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n      } else {\n        this.constraints = false;\n      }\n    }\n    this.elastic = resolveDragElastic(dragElastic);\n    /**\n     * If we're outputting to external MotionValues, we want to rebase the measured constraints\n     * from viewport-relative to component-relative.\n     */\n    if (prevConstraints !== this.constraints && layout && this.constraints && !this.hasMutatedConstraints) {\n      eachAxis(axis => {\n        if (this.constraints !== false && this.getAxisMotionValue(axis)) {\n          this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n        }\n      });\n    }\n  }\n  resolveRefConstraints() {\n    const {\n      dragConstraints: constraints,\n      onMeasureDragConstraints\n    } = this.getProps();\n    if (!constraints || !isRefObject(constraints)) return false;\n    const constraintsElement = constraints.current;\n    invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\", \"drag-constraints-ref\");\n    const {\n      projection\n    } = this.visualElement;\n    // TODO\n    if (!projection || !projection.layout) return false;\n    const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n    let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n    /**\n     * If there's an onMeasureDragConstraints listener we call it and\n     * if different constraints are returned, set constraints to that\n     */\n    if (onMeasureDragConstraints) {\n      const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n      this.hasMutatedConstraints = !!userConstraints;\n      if (userConstraints) {\n        measuredConstraints = convertBoundingBoxToBox(userConstraints);\n      }\n    }\n    return measuredConstraints;\n  }\n  startAnimation(velocity) {\n    const {\n      drag,\n      dragMomentum,\n      dragElastic,\n      dragTransition,\n      dragSnapToOrigin,\n      onDragTransitionEnd\n    } = this.getProps();\n    const constraints = this.constraints || {};\n    const momentumAnimations = eachAxis(axis => {\n      if (!shouldDrag(axis, drag, this.currentDirection)) {\n        return;\n      }\n      let transition = constraints && constraints[axis] || {};\n      if (dragSnapToOrigin) transition = {\n        min: 0,\n        max: 0\n      };\n      /**\n       * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n       * of spring animations so we should look into adding a disable spring option to `inertia`.\n       * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n       * using the value of `dragElastic`.\n       */\n      const bounceStiffness = dragElastic ? 200 : 1000000;\n      const bounceDamping = dragElastic ? 40 : 10000000;\n      const inertia = {\n        type: \"inertia\",\n        velocity: dragMomentum ? velocity[axis] : 0,\n        bounceStiffness,\n        bounceDamping,\n        timeConstant: 750,\n        restDelta: 1,\n        restSpeed: 10,\n        ...dragTransition,\n        ...transition\n      };\n      // If we're not animating on an externally-provided `MotionValue` we can use the\n      // component's animation controls which will handle interactions with whileHover (etc),\n      // otherwise we just have to animate the `MotionValue` itself.\n      return this.startAxisValueAnimation(axis, inertia);\n    });\n    // Run all animations and then resolve the new drag constraints.\n    return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n  }\n  startAxisValueAnimation(axis, transition) {\n    const axisValue = this.getAxisMotionValue(axis);\n    addValueToWillChange(this.visualElement, axis);\n    return axisValue.start(animateMotionValue(axis, axisValue, 0, transition, this.visualElement, false));\n  }\n  stopAnimation() {\n    eachAxis(axis => this.getAxisMotionValue(axis).stop());\n  }\n  pauseAnimation() {\n    eachAxis(axis => this.getAxisMotionValue(axis).animation?.pause());\n  }\n  getAnimationState(axis) {\n    return this.getAxisMotionValue(axis).animation?.state;\n  }\n  /**\n   * Drag works differently depending on which props are provided.\n   *\n   * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n   * - Otherwise, we apply the delta to the x/y motion values.\n   */\n  getAxisMotionValue(axis) {\n    const dragKey = `_drag${axis.toUpperCase()}`;\n    const props = this.visualElement.getProps();\n    const externalMotionValue = props[dragKey];\n    return externalMotionValue ? externalMotionValue : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n  }\n  snapToCursor(point) {\n    eachAxis(axis => {\n      const {\n        drag\n      } = this.getProps();\n      // If we're not dragging this axis, do an early return.\n      if (!shouldDrag(axis, drag, this.currentDirection)) return;\n      const {\n        projection\n      } = this.visualElement;\n      const axisValue = this.getAxisMotionValue(axis);\n      if (projection && projection.layout) {\n        const {\n          min,\n          max\n        } = projection.layout.layoutBox[axis];\n        axisValue.set(point[axis] - mixNumber(min, max, 0.5));\n      }\n    });\n  }\n  /**\n   * When the viewport resizes we want to check if the measured constraints\n   * have changed and, if so, reposition the element within those new constraints\n   * relative to where it was before the resize.\n   */\n  scalePositionWithinConstraints() {\n    if (!this.visualElement.current) return;\n    const {\n      drag,\n      dragConstraints\n    } = this.getProps();\n    const {\n      projection\n    } = this.visualElement;\n    if (!isRefObject(dragConstraints) || !projection || !this.constraints) return;\n    /**\n     * Stop current animations as there can be visual glitching if we try to do\n     * this mid-animation\n     */\n    this.stopAnimation();\n    /**\n     * Record the relative position of the dragged element relative to the\n     * constraints box and save as a progress value.\n     */\n    const boxProgress = {\n      x: 0,\n      y: 0\n    };\n    eachAxis(axis => {\n      const axisValue = this.getAxisMotionValue(axis);\n      if (axisValue && this.constraints !== false) {\n        const latest = axisValue.get();\n        boxProgress[axis] = calcOrigin({\n          min: latest,\n          max: latest\n        }, this.constraints[axis]);\n      }\n    });\n    /**\n     * Update the layout of this element and resolve the latest drag constraints\n     */\n    const {\n      transformTemplate\n    } = this.visualElement.getProps();\n    this.visualElement.current.style.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n    projection.root && projection.root.updateScroll();\n    projection.updateLayout();\n    this.resolveConstraints();\n    /**\n     * For each axis, calculate the current progress of the layout axis\n     * within the new constraints.\n     */\n    eachAxis(axis => {\n      if (!shouldDrag(axis, drag, null)) return;\n      /**\n       * Calculate a new transform based on the previous box progress\n       */\n      const axisValue = this.getAxisMotionValue(axis);\n      const {\n        min,\n        max\n      } = this.constraints[axis];\n      axisValue.set(mixNumber(min, max, boxProgress[axis]));\n    });\n  }\n  addListeners() {\n    if (!this.visualElement.current) return;\n    elementDragControls.set(this.visualElement, this);\n    const element = this.visualElement.current;\n    /**\n     * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n     */\n    const stopPointerListener = addPointerEvent(element, \"pointerdown\", event => {\n      const {\n        drag,\n        dragListener = true\n      } = this.getProps();\n      drag && dragListener && this.start(event);\n    });\n    const measureDragConstraints = () => {\n      const {\n        dragConstraints\n      } = this.getProps();\n      if (isRefObject(dragConstraints) && dragConstraints.current) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    };\n    const {\n      projection\n    } = this.visualElement;\n    const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n    if (projection && !projection.layout) {\n      projection.root && projection.root.updateScroll();\n      projection.updateLayout();\n    }\n    frame.read(measureDragConstraints);\n    /**\n     * Attach a window resize listener to scale the draggable target within its defined\n     * constraints as the window resizes.\n     */\n    const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n    /**\n     * If the element's layout changes, calculate the delta and apply that to\n     * the drag gesture's origin point.\n     */\n    const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", ({\n      delta,\n      hasLayoutChanged\n    }) => {\n      if (this.isDragging && hasLayoutChanged) {\n        eachAxis(axis => {\n          const motionValue = this.getAxisMotionValue(axis);\n          if (!motionValue) return;\n          this.originPoint[axis] += delta[axis].translate;\n          motionValue.set(motionValue.get() + delta[axis].translate);\n        });\n        this.visualElement.render();\n      }\n    });\n    return () => {\n      stopResizeListener();\n      stopPointerListener();\n      stopMeasureLayoutListener();\n      stopLayoutUpdateListener && stopLayoutUpdateListener();\n    };\n  }\n  getProps() {\n    const props = this.visualElement.getProps();\n    const {\n      drag = false,\n      dragDirectionLock = false,\n      dragPropagation = false,\n      dragConstraints = false,\n      dragElastic = defaultElastic,\n      dragMomentum = true\n    } = props;\n    return {\n      ...props,\n      drag,\n      dragDirectionLock,\n      dragPropagation,\n      dragConstraints,\n      dragElastic,\n      dragMomentum\n    };\n  }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n  return (drag === true || drag === direction) && (currentDirection === null || currentDirection === direction);\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n  let direction = null;\n  if (Math.abs(offset.y) > lockThreshold) {\n    direction = \"y\";\n  } else if (Math.abs(offset.x) > lockThreshold) {\n    direction = \"x\";\n  }\n  return direction;\n}\nexport { VisualElementDragControls, elementDragControls };", "map": {"version": 3, "names": ["frame", "mixNumber", "setDragLock", "percent", "invariant", "animateMotionValue", "addDomEvent", "addPointerEvent", "extractEventInfo", "convertBoxToBoundingBox", "convertBoundingBoxToBox", "calcLength", "createBox", "eachAxis", "measurePageBox", "getContextWindow", "isRefObject", "addValueToWillChange", "PanSession", "applyConstraints", "calcRelativeConstraints", "resolveDragElastic", "rebaseAxisConstraints", "calcViewportConstraints", "calcOrigin", "defaultElastic", "elementDragControls", "WeakMap", "VisualElementDragControls", "constructor", "visualElement", "openDragLock", "isDragging", "currentDirection", "originPoint", "x", "y", "constraints", "hasMutatedConstraints", "elastic", "latestPointerEvent", "latestPanInfo", "start", "originEvent", "snapToCursor", "distanceThreshold", "presenceContext", "isPresent", "onSessionStart", "event", "dragSnapToO<PERSON>in", "getProps", "pauseAnimation", "stopAnimation", "point", "onStart", "info", "drag", "dragPropagation", "onDragStart", "resolveConstraints", "projection", "isAnimationBlocked", "target", "undefined", "axis", "current", "getAxisMotionValue", "get", "test", "layout", "measuredAxis", "layoutBox", "length", "parseFloat", "postRender", "animationState", "setActive", "onMove", "dragDirectionLock", "onDirectionLock", "onDrag", "offset", "getCurrentDirection", "updateAxis", "render", "onSessionEnd", "stop", "resumeAnimation", "getAnimationState", "animation", "play", "panSession", "transformPagePoint", "getTransformPagePoint", "contextWindow", "panInfo", "finalEvent", "finalPanInfo", "cancel", "velocity", "startAnimation", "onDragEnd", "end", "_point", "shouldDrag", "axisValue", "next", "set", "dragConstraints", "dragElastic", "measure", "prevConstraints", "resolveRefConstraints", "onMeasureDragConstraints", "constraintsElement", "constraintsBox", "root", "measuredConstraints", "userConstraints", "dragMomentum", "dragTransition", "onDragTransitionEnd", "momentumAnimations", "transition", "min", "max", "bounceStiffness", "bounceDamping", "inertia", "type", "timeConstant", "restDelta", "restSpeed", "startAxisValueAnimation", "Promise", "all", "then", "pause", "state", "drag<PERSON>ey", "toUpperCase", "props", "externalMotionValue", "getValue", "initial", "scalePositionWithinConstraints", "boxProgress", "latest", "transformTemplate", "style", "transform", "updateScroll", "updateLayout", "addListeners", "element", "stopPointerListener", "dragListener", "measureDragConstraints", "stopMeasureLayoutListener", "addEventListener", "read", "stopResizeListener", "window", "stopLayoutUpdateListener", "delta", "hasLayoutChanged", "motionValue", "translate", "direction", "lockThreshold", "Math", "abs"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs"], "sourcesContent": ["import { frame, mixNumber, setDragLock, percent } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\n\nconst elementDragControls = new WeakMap();\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        this.openDragLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        /**\n         * The latest pointer event. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPointerEvent = null;\n        /**\n         * The latest pan info. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPanInfo = null;\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false, distanceThreshold } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event).point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openDragLock)\n                    this.openDragLock();\n                this.openDragLock = setDragLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openDragLock)\n                    return;\n            }\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = calcLength(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                frame.postRender(() => onDragStart(event, info));\n            }\n            addValueToWillChange(this.visualElement, \"transform\");\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openDragLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.stop(event, info);\n            this.latestPointerEvent = null;\n            this.latestPanInfo = null;\n        };\n        const resumeAnimation = () => eachAxis((axis) => this.getAnimationState(axis) === \"paused\" &&\n            this.getAxisMotionValue(axis).animation?.play());\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            distanceThreshold,\n            contextWindow: getContextWindow(this.visualElement),\n        });\n    }\n    /**\n     * @internal\n     */\n    stop(event, panInfo) {\n        const finalEvent = event || this.latestPointerEvent;\n        const finalPanInfo = panInfo || this.latestPanInfo;\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging || !finalPanInfo || !finalEvent)\n            return;\n        const { velocity } = finalPanInfo;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            frame.postRender(() => onDragEnd(finalEvent, finalPanInfo));\n        }\n    }\n    /**\n     * @internal\n     */\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openDragLock) {\n            this.openDragLock();\n            this.openDragLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : this.visualElement.projection?.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.constraints !== false &&\n                    this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\", \"drag-constraints-ref\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        addValueToWillChange(this.visualElement, axis);\n        return axisValue.start(animateMotionValue(axis, axisValue, 0, transition, this.visualElement, false));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).animation?.pause());\n    }\n    getAnimationState(axis) {\n        return this.getAxisMotionValue(axis).animation?.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = `_drag${axis.toUpperCase()}`;\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial\n                ? props.initial[axis]\n                : undefined) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mixNumber(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue && this.constraints !== false) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mixNumber(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints) && dragConstraints.current) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        frame.read(measureDragConstraints);\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,YAAY;AACnE,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,0CAA0C;AAC3G,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,oBAAoB,QAAQ,iDAAiD;AACtF,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,gBAAgB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,cAAc,QAAQ,yBAAyB;AAEnL,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzC,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG3B,SAAS,CAAC,CAAC;IAC1B;AACR;AACA;IACQ,IAAI,CAAC4B,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACX,aAAa,GAAGA,aAAa;EACtC;EACAY,KAAKA,CAACC,WAAW,EAAE;IAAEC,YAAY,GAAG,KAAK;IAAEC;EAAkB,CAAC,GAAG,CAAC,CAAC,EAAE;IACjE;AACR;AACA;IACQ,MAAM;MAAEC;IAAgB,CAAC,GAAG,IAAI,CAAChB,aAAa;IAC9C,IAAIgB,eAAe,IAAIA,eAAe,CAACC,SAAS,KAAK,KAAK,EACtD;IACJ,MAAMC,cAAc,GAAIC,KAAK,IAAK;MAC9B,MAAM;QAAEC;MAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC5C;MACA;MACAD,gBAAgB,GAAG,IAAI,CAACE,cAAc,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MAC/D,IAAIT,YAAY,EAAE;QACd,IAAI,CAACA,YAAY,CAACpC,gBAAgB,CAACyC,KAAK,CAAC,CAACK,KAAK,CAAC;MACpD;IACJ,CAAC;IACD,MAAMC,OAAO,GAAGA,CAACN,KAAK,EAAEO,IAAI,KAAK;MAC7B;MACA,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACR,QAAQ,CAAC,CAAC;MAC9D,IAAIM,IAAI,IAAI,CAACC,eAAe,EAAE;QAC1B,IAAI,IAAI,CAAC3B,YAAY,EACjB,IAAI,CAACA,YAAY,CAAC,CAAC;QACvB,IAAI,CAACA,YAAY,GAAG7B,WAAW,CAACuD,IAAI,CAAC;QACrC;QACA,IAAI,CAAC,IAAI,CAAC1B,YAAY,EAClB;MACR;MACA,IAAI,CAACS,kBAAkB,GAAGS,KAAK;MAC/B,IAAI,CAACR,aAAa,GAAGe,IAAI;MACzB,IAAI,CAACxB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC2B,kBAAkB,CAAC,CAAC;MACzB,IAAI,IAAI,CAAC9B,aAAa,CAAC+B,UAAU,EAAE;QAC/B,IAAI,CAAC/B,aAAa,CAAC+B,UAAU,CAACC,kBAAkB,GAAG,IAAI;QACvD,IAAI,CAAChC,aAAa,CAAC+B,UAAU,CAACE,MAAM,GAAGC,SAAS;MACpD;MACA;AACZ;AACA;MACYnD,QAAQ,CAAEoD,IAAI,IAAK;QACf,IAAIC,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,CAAC,IAAI,CAAC;QACtD;AAChB;AACA;QACgB,IAAIjE,OAAO,CAACkE,IAAI,CAACH,OAAO,CAAC,EAAE;UACvB,MAAM;YAAEL;UAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;UACzC,IAAI+B,UAAU,IAAIA,UAAU,CAACS,MAAM,EAAE;YACjC,MAAMC,YAAY,GAAGV,UAAU,CAACS,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;YACtD,IAAIM,YAAY,EAAE;cACd,MAAME,MAAM,GAAG9D,UAAU,CAAC4D,YAAY,CAAC;cACvCL,OAAO,GAAGO,MAAM,IAAIC,UAAU,CAACR,OAAO,CAAC,GAAG,GAAG,CAAC;YAClD;UACJ;QACJ;QACA,IAAI,CAAChC,WAAW,CAAC+B,IAAI,CAAC,GAAGC,OAAO;MACpC,CAAC,CAAC;MACF;MACA,IAAIP,WAAW,EAAE;QACb3D,KAAK,CAAC2E,UAAU,CAAC,MAAMhB,WAAW,CAACV,KAAK,EAAEO,IAAI,CAAC,CAAC;MACpD;MACAvC,oBAAoB,CAAC,IAAI,CAACa,aAAa,EAAE,WAAW,CAAC;MACrD,MAAM;QAAE8C;MAAe,CAAC,GAAG,IAAI,CAAC9C,aAAa;MAC7C8C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;IACjE,CAAC;IACD,MAAMC,MAAM,GAAGA,CAAC7B,KAAK,EAAEO,IAAI,KAAK;MAC5B,IAAI,CAAChB,kBAAkB,GAAGS,KAAK;MAC/B,IAAI,CAACR,aAAa,GAAGe,IAAI;MACzB,MAAM;QAAEE,eAAe;QAAEqB,iBAAiB;QAAEC,eAAe;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAAC9B,QAAQ,CAAC,CAAC;MACxF;MACA,IAAI,CAACO,eAAe,IAAI,CAAC,IAAI,CAAC3B,YAAY,EACtC;MACJ,MAAM;QAAEmD;MAAO,CAAC,GAAG1B,IAAI;MACvB;MACA,IAAIuB,iBAAiB,IAAI,IAAI,CAAC9C,gBAAgB,KAAK,IAAI,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAGkD,mBAAmB,CAACD,MAAM,CAAC;QACnD;QACA,IAAI,IAAI,CAACjD,gBAAgB,KAAK,IAAI,EAAE;UAChC+C,eAAe,IAAIA,eAAe,CAAC,IAAI,CAAC/C,gBAAgB,CAAC;QAC7D;QACA;MACJ;MACA;MACA,IAAI,CAACmD,UAAU,CAAC,GAAG,EAAE5B,IAAI,CAACF,KAAK,EAAE4B,MAAM,CAAC;MACxC,IAAI,CAACE,UAAU,CAAC,GAAG,EAAE5B,IAAI,CAACF,KAAK,EAAE4B,MAAM,CAAC;MACxC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACpD,aAAa,CAACuD,MAAM,CAAC,CAAC;MAC3B;AACZ;AACA;AACA;MACYJ,MAAM,IAAIA,MAAM,CAAChC,KAAK,EAAEO,IAAI,CAAC;IACjC,CAAC;IACD,MAAM8B,YAAY,GAAGA,CAACrC,KAAK,EAAEO,IAAI,KAAK;MAClC,IAAI,CAAChB,kBAAkB,GAAGS,KAAK;MAC/B,IAAI,CAACR,aAAa,GAAGe,IAAI;MACzB,IAAI,CAAC+B,IAAI,CAACtC,KAAK,EAAEO,IAAI,CAAC;MACtB,IAAI,CAAChB,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACC,aAAa,GAAG,IAAI;IAC7B,CAAC;IACD,MAAM+C,eAAe,GAAGA,CAAA,KAAM3E,QAAQ,CAAEoD,IAAI,IAAK,IAAI,CAACwB,iBAAiB,CAACxB,IAAI,CAAC,KAAK,QAAQ,IACtF,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,EAAEC,IAAI,CAAC,CAAC,CAAC;IACpD,MAAM;MAAEzC;IAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC5C,IAAI,CAACyC,UAAU,GAAG,IAAI1E,UAAU,CAACyB,WAAW,EAAE;MAC1CK,cAAc;MACdO,OAAO;MACPuB,MAAM;MACNQ,YAAY;MACZE;IACJ,CAAC,EAAE;MACCK,kBAAkB,EAAE,IAAI,CAAC/D,aAAa,CAACgE,qBAAqB,CAAC,CAAC;MAC9D5C,gBAAgB;MAChBL,iBAAiB;MACjBkD,aAAa,EAAEhF,gBAAgB,CAAC,IAAI,CAACe,aAAa;IACtD,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIyD,IAAIA,CAACtC,KAAK,EAAE+C,OAAO,EAAE;IACjB,MAAMC,UAAU,GAAGhD,KAAK,IAAI,IAAI,CAACT,kBAAkB;IACnD,MAAM0D,YAAY,GAAGF,OAAO,IAAI,IAAI,CAACvD,aAAa;IAClD,MAAMT,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAACmE,MAAM,CAAC,CAAC;IACb,IAAI,CAACnE,UAAU,IAAI,CAACkE,YAAY,IAAI,CAACD,UAAU,EAC3C;IACJ,MAAM;MAAEG;IAAS,CAAC,GAAGF,YAAY;IACjC,IAAI,CAACG,cAAc,CAACD,QAAQ,CAAC;IAC7B,MAAM;MAAEE;IAAU,CAAC,GAAG,IAAI,CAACnD,QAAQ,CAAC,CAAC;IACrC,IAAImD,SAAS,EAAE;MACXtG,KAAK,CAAC2E,UAAU,CAAC,MAAM2B,SAAS,CAACL,UAAU,EAAEC,YAAY,CAAC,CAAC;IAC/D;EACJ;EACA;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACnE,UAAU,GAAG,KAAK;IACvB,MAAM;MAAE6B,UAAU;MAAEe;IAAe,CAAC,GAAG,IAAI,CAAC9C,aAAa;IACzD,IAAI+B,UAAU,EAAE;MACZA,UAAU,CAACC,kBAAkB,GAAG,KAAK;IACzC;IACA,IAAI,CAAC8B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACW,GAAG,CAAC,CAAC;IACxC,IAAI,CAACX,UAAU,GAAG5B,SAAS;IAC3B,MAAM;MAAEN;IAAgB,CAAC,GAAG,IAAI,CAACP,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACO,eAAe,IAAI,IAAI,CAAC3B,YAAY,EAAE;MACvC,IAAI,CAACA,YAAY,CAAC,CAAC;MACnB,IAAI,CAACA,YAAY,GAAG,IAAI;IAC5B;IACA6C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC;EAClE;EACAO,UAAUA,CAACnB,IAAI,EAAEuC,MAAM,EAAEtB,MAAM,EAAE;IAC7B,MAAM;MAAEzB;IAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;IAChC;IACA,IAAI,CAAC+B,MAAM,IAAI,CAACuB,UAAU,CAACxC,IAAI,EAAER,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EACzD;IACJ,MAAMyE,SAAS,GAAG,IAAI,CAACvC,kBAAkB,CAACF,IAAI,CAAC;IAC/C,IAAI0C,IAAI,GAAG,IAAI,CAACzE,WAAW,CAAC+B,IAAI,CAAC,GAAGiB,MAAM,CAACjB,IAAI,CAAC;IAChD;IACA,IAAI,IAAI,CAAC5B,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC4B,IAAI,CAAC,EAAE;MAC5C0C,IAAI,GAAGxF,gBAAgB,CAACwF,IAAI,EAAE,IAAI,CAACtE,WAAW,CAAC4B,IAAI,CAAC,EAAE,IAAI,CAAC1B,OAAO,CAAC0B,IAAI,CAAC,CAAC;IAC7E;IACAyC,SAAS,CAACE,GAAG,CAACD,IAAI,CAAC;EACvB;EACA/C,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAEiD,eAAe;MAAEC;IAAY,CAAC,GAAG,IAAI,CAAC3D,QAAQ,CAAC,CAAC;IACxD,MAAMmB,MAAM,GAAG,IAAI,CAACxC,aAAa,CAAC+B,UAAU,IACxC,CAAC,IAAI,CAAC/B,aAAa,CAAC+B,UAAU,CAACS,MAAM,GACnC,IAAI,CAACxC,aAAa,CAAC+B,UAAU,CAACkD,OAAO,CAAC,KAAK,CAAC,GAC5C,IAAI,CAACjF,aAAa,CAAC+B,UAAU,EAAES,MAAM;IAC3C,MAAM0C,eAAe,GAAG,IAAI,CAAC3E,WAAW;IACxC,IAAIwE,eAAe,IAAI7F,WAAW,CAAC6F,eAAe,CAAC,EAAE;MACjD,IAAI,CAAC,IAAI,CAACxE,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,IAAI,CAAC4E,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC,MACI;MACD,IAAIJ,eAAe,IAAIvC,MAAM,EAAE;QAC3B,IAAI,CAACjC,WAAW,GAAGjB,uBAAuB,CAACkD,MAAM,CAACE,SAAS,EAAEqC,eAAe,CAAC;MACjF,CAAC,MACI;QACD,IAAI,CAACxE,WAAW,GAAG,KAAK;MAC5B;IACJ;IACA,IAAI,CAACE,OAAO,GAAGlB,kBAAkB,CAACyF,WAAW,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAIE,eAAe,KAAK,IAAI,CAAC3E,WAAW,IACpCiC,MAAM,IACN,IAAI,CAACjC,WAAW,IAChB,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7BzB,QAAQ,CAAEoD,IAAI,IAAK;QACf,IAAI,IAAI,CAAC5B,WAAW,KAAK,KAAK,IAC1B,IAAI,CAAC8B,kBAAkB,CAACF,IAAI,CAAC,EAAE;UAC/B,IAAI,CAAC5B,WAAW,CAAC4B,IAAI,CAAC,GAAG3C,qBAAqB,CAACgD,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC,EAAE,IAAI,CAAC5B,WAAW,CAAC4B,IAAI,CAAC,CAAC;QAClG;MACJ,CAAC,CAAC;IACN;EACJ;EACAgD,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEJ,eAAe,EAAExE,WAAW;MAAE6E;IAAyB,CAAC,GAAG,IAAI,CAAC/D,QAAQ,CAAC,CAAC;IAClF,IAAI,CAACd,WAAW,IAAI,CAACrB,WAAW,CAACqB,WAAW,CAAC,EACzC,OAAO,KAAK;IAChB,MAAM8E,kBAAkB,GAAG9E,WAAW,CAAC6B,OAAO;IAC9C9D,SAAS,CAAC+G,kBAAkB,KAAK,IAAI,EAAE,wGAAwG,EAAE,sBAAsB,CAAC;IACxK,MAAM;MAAEtD;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC;IACA,IAAI,CAAC+B,UAAU,IAAI,CAACA,UAAU,CAACS,MAAM,EACjC,OAAO,KAAK;IAChB,MAAM8C,cAAc,GAAGtG,cAAc,CAACqG,kBAAkB,EAAEtD,UAAU,CAACwD,IAAI,EAAE,IAAI,CAACvF,aAAa,CAACgE,qBAAqB,CAAC,CAAC,CAAC;IACtH,IAAIwB,mBAAmB,GAAG/F,uBAAuB,CAACsC,UAAU,CAACS,MAAM,CAACE,SAAS,EAAE4C,cAAc,CAAC;IAC9F;AACR;AACA;AACA;IACQ,IAAIF,wBAAwB,EAAE;MAC1B,MAAMK,eAAe,GAAGL,wBAAwB,CAACzG,uBAAuB,CAAC6G,mBAAmB,CAAC,CAAC;MAC9F,IAAI,CAAChF,qBAAqB,GAAG,CAAC,CAACiF,eAAe;MAC9C,IAAIA,eAAe,EAAE;QACjBD,mBAAmB,GAAG5G,uBAAuB,CAAC6G,eAAe,CAAC;MAClE;IACJ;IACA,OAAOD,mBAAmB;EAC9B;EACAjB,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAM;MAAE3C,IAAI;MAAE+D,YAAY;MAAEV,WAAW;MAAEW,cAAc;MAAEvE,gBAAgB;MAAEwE;IAAqB,CAAC,GAAG,IAAI,CAACvE,QAAQ,CAAC,CAAC;IACnH,MAAMd,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IAC1C,MAAMsF,kBAAkB,GAAG9G,QAAQ,CAAEoD,IAAI,IAAK;MAC1C,IAAI,CAACwC,UAAU,CAACxC,IAAI,EAAER,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EAAE;QAChD;MACJ;MACA,IAAI2F,UAAU,GAAIvF,WAAW,IAAIA,WAAW,CAAC4B,IAAI,CAAC,IAAK,CAAC,CAAC;MACzD,IAAIf,gBAAgB,EAChB0E,UAAU,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,eAAe,GAAGjB,WAAW,GAAG,GAAG,GAAG,OAAO;MACnD,MAAMkB,aAAa,GAAGlB,WAAW,GAAG,EAAE,GAAG,QAAQ;MACjD,MAAMmB,OAAO,GAAG;QACZC,IAAI,EAAE,SAAS;QACf9B,QAAQ,EAAEoB,YAAY,GAAGpB,QAAQ,CAACnC,IAAI,CAAC,GAAG,CAAC;QAC3C8D,eAAe;QACfC,aAAa;QACbG,YAAY,EAAE,GAAG;QACjBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,EAAE;QACb,GAAGZ,cAAc;QACjB,GAAGG;MACP,CAAC;MACD;MACA;MACA;MACA,OAAO,IAAI,CAACU,uBAAuB,CAACrE,IAAI,EAAEgE,OAAO,CAAC;IACtD,CAAC,CAAC;IACF;IACA,OAAOM,OAAO,CAACC,GAAG,CAACb,kBAAkB,CAAC,CAACc,IAAI,CAACf,mBAAmB,CAAC;EACpE;EACAY,uBAAuBA,CAACrE,IAAI,EAAE2D,UAAU,EAAE;IACtC,MAAMlB,SAAS,GAAG,IAAI,CAACvC,kBAAkB,CAACF,IAAI,CAAC;IAC/ChD,oBAAoB,CAAC,IAAI,CAACa,aAAa,EAAEmC,IAAI,CAAC;IAC9C,OAAOyC,SAAS,CAAChE,KAAK,CAACrC,kBAAkB,CAAC4D,IAAI,EAAEyC,SAAS,EAAE,CAAC,EAAEkB,UAAU,EAAE,IAAI,CAAC9F,aAAa,EAAE,KAAK,CAAC,CAAC;EACzG;EACAuB,aAAaA,CAAA,EAAG;IACZxC,QAAQ,CAAEoD,IAAI,IAAK,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACsB,IAAI,CAAC,CAAC,CAAC;EAC5D;EACAnC,cAAcA,CAAA,EAAG;IACbvC,QAAQ,CAAEoD,IAAI,IAAK,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,EAAEgD,KAAK,CAAC,CAAC,CAAC;EACxE;EACAjD,iBAAiBA,CAACxB,IAAI,EAAE;IACpB,OAAO,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,EAAEiD,KAAK;EACzD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIxE,kBAAkBA,CAACF,IAAI,EAAE;IACrB,MAAM2E,OAAO,GAAG,QAAQ3E,IAAI,CAAC4E,WAAW,CAAC,CAAC,EAAE;IAC5C,MAAMC,KAAK,GAAG,IAAI,CAAChH,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3C,MAAM4F,mBAAmB,GAAGD,KAAK,CAACF,OAAO,CAAC;IAC1C,OAAOG,mBAAmB,GACpBA,mBAAmB,GACnB,IAAI,CAACjH,aAAa,CAACkH,QAAQ,CAAC/E,IAAI,EAAE,CAAC6E,KAAK,CAACG,OAAO,GAC5CH,KAAK,CAACG,OAAO,CAAChF,IAAI,CAAC,GACnBD,SAAS,KAAK,CAAC,CAAC;EAC9B;EACApB,YAAYA,CAACU,KAAK,EAAE;IAChBzC,QAAQ,CAAEoD,IAAI,IAAK;MACf,MAAM;QAAER;MAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;MAChC;MACA,IAAI,CAACsD,UAAU,CAACxC,IAAI,EAAER,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EAC9C;MACJ,MAAM;QAAE4B;MAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;MACzC,MAAM4E,SAAS,GAAG,IAAI,CAACvC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIJ,UAAU,IAAIA,UAAU,CAACS,MAAM,EAAE;QACjC,MAAM;UAAEuD,GAAG;UAAEC;QAAI,CAAC,GAAGjE,UAAU,CAACS,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;QACtDyC,SAAS,CAACE,GAAG,CAACtD,KAAK,CAACW,IAAI,CAAC,GAAGhE,SAAS,CAAC4H,GAAG,EAAEC,GAAG,EAAE,GAAG,CAAC,CAAC;MACzD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIoB,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,CAAC,IAAI,CAACpH,aAAa,CAACoC,OAAO,EAC3B;IACJ,MAAM;MAAET,IAAI;MAAEoD;IAAgB,CAAC,GAAG,IAAI,CAAC1D,QAAQ,CAAC,CAAC;IACjD,MAAM;MAAEU;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC,IAAI,CAACd,WAAW,CAAC6F,eAAe,CAAC,IAAI,CAAChD,UAAU,IAAI,CAAC,IAAI,CAACxB,WAAW,EACjE;IACJ;AACR;AACA;AACA;IACQ,IAAI,CAACgB,aAAa,CAAC,CAAC;IACpB;AACR;AACA;AACA;IACQ,MAAM8F,WAAW,GAAG;MAAEhH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAClCvB,QAAQ,CAAEoD,IAAI,IAAK;MACf,MAAMyC,SAAS,GAAG,IAAI,CAACvC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIyC,SAAS,IAAI,IAAI,CAACrE,WAAW,KAAK,KAAK,EAAE;QACzC,MAAM+G,MAAM,GAAG1C,SAAS,CAACtC,GAAG,CAAC,CAAC;QAC9B+E,WAAW,CAAClF,IAAI,CAAC,GAAGzC,UAAU,CAAC;UAAEqG,GAAG,EAAEuB,MAAM;UAAEtB,GAAG,EAAEsB;QAAO,CAAC,EAAE,IAAI,CAAC/G,WAAW,CAAC4B,IAAI,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;IACF;AACR;AACA;IACQ,MAAM;MAAEoF;IAAkB,CAAC,GAAG,IAAI,CAACvH,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAACrB,aAAa,CAACoC,OAAO,CAACoF,KAAK,CAACC,SAAS,GAAGF,iBAAiB,GACxDA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;IACZxF,UAAU,CAACwD,IAAI,IAAIxD,UAAU,CAACwD,IAAI,CAACmC,YAAY,CAAC,CAAC;IACjD3F,UAAU,CAAC4F,YAAY,CAAC,CAAC;IACzB,IAAI,CAAC7F,kBAAkB,CAAC,CAAC;IACzB;AACR;AACA;AACA;IACQ/C,QAAQ,CAAEoD,IAAI,IAAK;MACf,IAAI,CAACwC,UAAU,CAACxC,IAAI,EAAER,IAAI,EAAE,IAAI,CAAC,EAC7B;MACJ;AACZ;AACA;MACY,MAAMiD,SAAS,GAAG,IAAI,CAACvC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,MAAM;QAAE4D,GAAG;QAAEC;MAAI,CAAC,GAAG,IAAI,CAACzF,WAAW,CAAC4B,IAAI,CAAC;MAC3CyC,SAAS,CAACE,GAAG,CAAC3G,SAAS,CAAC4H,GAAG,EAAEC,GAAG,EAAEqB,WAAW,CAAClF,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;EACN;EACAyF,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC5H,aAAa,CAACoC,OAAO,EAC3B;IACJxC,mBAAmB,CAACkF,GAAG,CAAC,IAAI,CAAC9E,aAAa,EAAE,IAAI,CAAC;IACjD,MAAM6H,OAAO,GAAG,IAAI,CAAC7H,aAAa,CAACoC,OAAO;IAC1C;AACR;AACA;IACQ,MAAM0F,mBAAmB,GAAGrJ,eAAe,CAACoJ,OAAO,EAAE,aAAa,EAAG1G,KAAK,IAAK;MAC3E,MAAM;QAAEQ,IAAI;QAAEoG,YAAY,GAAG;MAAK,CAAC,GAAG,IAAI,CAAC1G,QAAQ,CAAC,CAAC;MACrDM,IAAI,IAAIoG,YAAY,IAAI,IAAI,CAACnH,KAAK,CAACO,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,MAAM6G,sBAAsB,GAAGA,CAAA,KAAM;MACjC,MAAM;QAAEjD;MAAgB,CAAC,GAAG,IAAI,CAAC1D,QAAQ,CAAC,CAAC;MAC3C,IAAInC,WAAW,CAAC6F,eAAe,CAAC,IAAIA,eAAe,CAAC3C,OAAO,EAAE;QACzD,IAAI,CAAC7B,WAAW,GAAG,IAAI,CAAC4E,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC;IACD,MAAM;MAAEpD;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC,MAAMiI,yBAAyB,GAAGlG,UAAU,CAACmG,gBAAgB,CAAC,SAAS,EAAEF,sBAAsB,CAAC;IAChG,IAAIjG,UAAU,IAAI,CAACA,UAAU,CAACS,MAAM,EAAE;MAClCT,UAAU,CAACwD,IAAI,IAAIxD,UAAU,CAACwD,IAAI,CAACmC,YAAY,CAAC,CAAC;MACjD3F,UAAU,CAAC4F,YAAY,CAAC,CAAC;IAC7B;IACAzJ,KAAK,CAACiK,IAAI,CAACH,sBAAsB,CAAC;IAClC;AACR;AACA;AACA;IACQ,MAAMI,kBAAkB,GAAG5J,WAAW,CAAC6J,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAACjB,8BAA8B,CAAC,CAAC,CAAC;IACrG;AACR;AACA;AACA;IACQ,MAAMkB,wBAAwB,GAAGvG,UAAU,CAACmG,gBAAgB,CAAC,WAAW,EAAG,CAAC;MAAEK,KAAK;MAAEC;IAAiB,CAAC,KAAK;MACxG,IAAI,IAAI,CAACtI,UAAU,IAAIsI,gBAAgB,EAAE;QACrCzJ,QAAQ,CAAEoD,IAAI,IAAK;UACf,MAAMsG,WAAW,GAAG,IAAI,CAACpG,kBAAkB,CAACF,IAAI,CAAC;UACjD,IAAI,CAACsG,WAAW,EACZ;UACJ,IAAI,CAACrI,WAAW,CAAC+B,IAAI,CAAC,IAAIoG,KAAK,CAACpG,IAAI,CAAC,CAACuG,SAAS;UAC/CD,WAAW,CAAC3D,GAAG,CAAC2D,WAAW,CAACnG,GAAG,CAAC,CAAC,GAAGiG,KAAK,CAACpG,IAAI,CAAC,CAACuG,SAAS,CAAC;QAC9D,CAAC,CAAC;QACF,IAAI,CAAC1I,aAAa,CAACuD,MAAM,CAAC,CAAC;MAC/B;IACJ,CAAE,CAAC;IACH,OAAO,MAAM;MACT6E,kBAAkB,CAAC,CAAC;MACpBN,mBAAmB,CAAC,CAAC;MACrBG,yBAAyB,CAAC,CAAC;MAC3BK,wBAAwB,IAAIA,wBAAwB,CAAC,CAAC;IAC1D,CAAC;EACL;EACAjH,QAAQA,CAAA,EAAG;IACP,MAAM2F,KAAK,GAAG,IAAI,CAAChH,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3C,MAAM;MAAEM,IAAI,GAAG,KAAK;MAAEsB,iBAAiB,GAAG,KAAK;MAAErB,eAAe,GAAG,KAAK;MAAEmD,eAAe,GAAG,KAAK;MAAEC,WAAW,GAAGrF,cAAc;MAAE+F,YAAY,GAAG;IAAM,CAAC,GAAGsB,KAAK;IAC/J,OAAO;MACH,GAAGA,KAAK;MACRrF,IAAI;MACJsB,iBAAiB;MACjBrB,eAAe;MACfmD,eAAe;MACfC,WAAW;MACXU;IACJ,CAAC;EACL;AACJ;AACA,SAASf,UAAUA,CAACgE,SAAS,EAAEhH,IAAI,EAAExB,gBAAgB,EAAE;EACnD,OAAQ,CAACwB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKgH,SAAS,MACvCxI,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKwI,SAAS,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAStF,mBAAmBA,CAACD,MAAM,EAAEwF,aAAa,GAAG,EAAE,EAAE;EACrD,IAAID,SAAS,GAAG,IAAI;EACpB,IAAIE,IAAI,CAACC,GAAG,CAAC1F,MAAM,CAAC9C,CAAC,CAAC,GAAGsI,aAAa,EAAE;IACpCD,SAAS,GAAG,GAAG;EACnB,CAAC,MACI,IAAIE,IAAI,CAACC,GAAG,CAAC1F,MAAM,CAAC/C,CAAC,CAAC,GAAGuI,aAAa,EAAE;IACzCD,SAAS,GAAG,GAAG;EACnB;EACA,OAAOA,SAAS;AACpB;AAEA,SAAS7I,yBAAyB,EAAEF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}