{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\layout\\\\Layout.tsx\";\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\nimport CustomCursor from './CustomCursor';\nimport Matrix<PERSON>ain from './MatrixRain';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-black text-white relative\",\n    children: [/*#__PURE__*/_jsxDEV(CustomCursor, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MatrixRain, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"pt-16 relative z-10\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Header", "Footer", "CustomCursor", "MatrixRain", "jsxDEV", "_jsxDEV", "Layout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\nimport CustomCursor from './CustomCursor';\nimport MatrixRain from './MatrixRain';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-black text-white relative\">\n      <CustomCursor />\n      <MatrixRain />\n      <Header />\n      <main className=\"pt-16 relative z-10\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtC,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EACtD,oBACEF,OAAA;IAAKG,SAAS,EAAC,2CAA2C;IAAAD,QAAA,gBACxDF,OAAA,CAACH,YAAY;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBP,OAAA,CAACF,UAAU;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdP,OAAA,CAACL,MAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVP,OAAA;MAAMG,SAAS,EAAC,qBAAqB;MAAAD,QAAA,EAClCA;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACPP,OAAA,CAACJ,MAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACC,EAAA,GAZIP,MAA6B;AAcnC,eAAeA,MAAM;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}