{"ast": null, "code": "import { memo } from 'motion-utils';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\", \"clipPath\", \"filter\", \"transform\"\n// TODO: Could be re-enabled now we have support for linear() easing\n// \"background-color\"\n]);\nconst supportsWaapi = /*@__PURE__*/memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\nfunction supportsBrowserAnimation(options) {\n  const {\n    motionValue,\n    name,\n    repeatDelay,\n    repeatType,\n    damping,\n    type\n  } = options;\n  const subject = motionValue?.owner?.current;\n  /**\n   * We use this check instead of isHTMLElement() because we explicitly\n   * **don't** want elements in different timing contexts (i.e. popups)\n   * to be accelerated, as it's not possible to sync these animations\n   * properly with those driven from the main window frameloop.\n   */\n  if (!(subject instanceof HTMLElement)) {\n    return false;\n  }\n  const {\n    onUpdate,\n    transformTemplate\n  } = motionValue.owner.getProps();\n  return supportsWaapi() && name && acceleratedValues.has(name) && (name !== \"transform\" || !transformTemplate) &&\n  /**\n   * If we're outputting values to onUpdate then we can't use WAAPI as there's\n   * no way to read the value from WAAPI every frame.\n   */\n  !onUpdate && !repeatDelay && repeatType !== \"mirror\" && damping !== 0 && type !== \"inertia\";\n}\nexport { supportsBrowserAnimation };", "map": {"version": 3, "names": ["memo", "acceleratedValues", "Set", "supportsWaapi", "Object", "hasOwnProperty", "call", "Element", "prototype", "supportsBrowserAnimation", "options", "motionValue", "name", "repeatDelay", "repeatType", "damping", "type", "subject", "owner", "current", "HTMLElement", "onUpdate", "transformTemplate", "getProps", "has"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/animation/waapi/supports/waapi.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\n\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Could be re-enabled now we have support for linear() easing\n    // \"background-color\"\n]);\nconst supportsWaapi = /*@__PURE__*/ memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\nfunction supportsBrowserAnimation(options) {\n    const { motionValue, name, repeatDelay, repeatType, damping, type } = options;\n    const subject = motionValue?.owner?.current;\n    /**\n     * We use this check instead of isHTMLElement() because we explicitly\n     * **don't** want elements in different timing contexts (i.e. popups)\n     * to be accelerated, as it's not possible to sync these animations\n     * properly with those driven from the main window frameloop.\n     */\n    if (!(subject instanceof HTMLElement)) {\n        return false;\n    }\n    const { onUpdate, transformTemplate } = motionValue.owner.getProps();\n    return (supportsWaapi() &&\n        name &&\n        acceleratedValues.has(name) &&\n        (name !== \"transform\" || !transformTemplate) &&\n        /**\n         * If we're outputting values to onUpdate then we can't use WAAPI as there's\n         * no way to read the value from WAAPI every frame.\n         */\n        !onUpdate &&\n        !repeatDelay &&\n        repeatType !== \"mirror\" &&\n        damping !== 0 &&\n        type !== \"inertia\");\n}\n\nexport { supportsBrowserAnimation };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;;AAEnC;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAC9B,SAAS,EACT,UAAU,EACV,QAAQ,EACR;AACA;AACA;AAAA,CACH,CAAC;AACF,MAAMC,aAAa,GAAG,aAAcH,IAAI,CAAC,MAAMI,MAAM,CAACC,cAAc,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,EAAE,SAAS,CAAC,CAAC;AACxG,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EACvC,MAAM;IAAEC,WAAW;IAAEC,IAAI;IAAEC,WAAW;IAAEC,UAAU;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGN,OAAO;EAC7E,MAAMO,OAAO,GAAGN,WAAW,EAAEO,KAAK,EAAEC,OAAO;EAC3C;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,EAAEF,OAAO,YAAYG,WAAW,CAAC,EAAE;IACnC,OAAO,KAAK;EAChB;EACA,MAAM;IAAEC,QAAQ;IAAEC;EAAkB,CAAC,GAAGX,WAAW,CAACO,KAAK,CAACK,QAAQ,CAAC,CAAC;EACpE,OAAQpB,aAAa,CAAC,CAAC,IACnBS,IAAI,IACJX,iBAAiB,CAACuB,GAAG,CAACZ,IAAI,CAAC,KAC1BA,IAAI,KAAK,WAAW,IAAI,CAACU,iBAAiB,CAAC;EAC5C;AACR;AACA;AACA;EACQ,CAACD,QAAQ,IACT,CAACR,WAAW,IACZC,UAAU,KAAK,QAAQ,IACvBC,OAAO,KAAK,CAAC,IACbC,IAAI,KAAK,SAAS;AAC1B;AAEA,SAASP,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}