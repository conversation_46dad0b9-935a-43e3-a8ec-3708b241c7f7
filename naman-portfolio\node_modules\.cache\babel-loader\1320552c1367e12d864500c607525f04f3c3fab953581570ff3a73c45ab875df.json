{"ast": null, "code": "import { formatErrorMessage } from './format-error-message.mjs';\nlet warning = () => {};\nlet invariant = () => {};\nif (process.env.NODE_ENV !== \"production\") {\n  warning = (check, message, errorCode) => {\n    if (!check && typeof console !== \"undefined\") {\n      console.warn(formatErrorMessage(message, errorCode));\n    }\n  };\n  invariant = (check, message, errorCode) => {\n    if (!check) {\n      throw new Error(formatErrorMessage(message, errorCode));\n    }\n  };\n}\nexport { invariant, warning };", "map": {"version": 3, "names": ["formatErrorMessage", "warning", "invariant", "process", "env", "NODE_ENV", "check", "message", "errorCode", "console", "warn", "Error"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["import { formatErrorMessage } from './format-error-message.mjs';\n\nlet warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message, errorCode) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(formatErrorMessage(message, errorCode));\n        }\n    };\n    invariant = (check, message, errorCode) => {\n        if (!check) {\n            throw new Error(formatErrorMessage(message, errorCode));\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,4BAA4B;AAE/D,IAAIC,OAAO,GAAGA,CAAA,KAAM,CAAE,CAAC;AACvB,IAAIC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACvCJ,OAAO,GAAGA,CAACK,KAAK,EAAEC,OAAO,EAAEC,SAAS,KAAK;IACrC,IAAI,CAACF,KAAK,IAAI,OAAOG,OAAO,KAAK,WAAW,EAAE;MAC1CA,OAAO,CAACC,IAAI,CAACV,kBAAkB,CAACO,OAAO,EAAEC,SAAS,CAAC,CAAC;IACxD;EACJ,CAAC;EACDN,SAAS,GAAGA,CAACI,KAAK,EAAEC,OAAO,EAAEC,SAAS,KAAK;IACvC,IAAI,CAACF,KAAK,EAAE;MACR,MAAM,IAAIK,KAAK,CAACX,kBAAkB,CAACO,OAAO,EAAEC,SAAS,CAAC,CAAC;IAC3D;EACJ,CAAC;AACL;AAEA,SAASN,SAAS,EAAED,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}