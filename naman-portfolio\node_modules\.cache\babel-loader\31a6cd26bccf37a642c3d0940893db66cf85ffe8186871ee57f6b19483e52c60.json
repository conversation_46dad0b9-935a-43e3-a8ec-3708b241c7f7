{"ast": null, "code": "function renderHTML(element, {\n  style,\n  vars\n}, styleProp, projection) {\n  const elementStyle = element.style;\n  let key;\n  for (key in style) {\n    // CSSStyleDeclaration has [index: number]: string; in the types, so we use that as key type.\n    elementStyle[key] = style[key];\n  }\n  // Write projection styles directly to element style\n  projection?.applyProjectionStyles(elementStyle, styleProp);\n  for (key in vars) {\n    // Loop over any CSS variables and assign those.\n    // They can only be assigned using `setProperty`.\n    elementStyle.setProperty(key, vars[key]);\n  }\n}\nexport { renderHTML };", "map": {"version": 3, "names": ["renderHTML", "element", "style", "vars", "styleProp", "projection", "elementStyle", "key", "applyProjectionStyles", "setProperty"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/html/utils/render.mjs"], "sourcesContent": ["function renderHTML(element, { style, vars }, styleProp, projection) {\n    const elementStyle = element.style;\n    let key;\n    for (key in style) {\n        // CSSStyleDeclaration has [index: number]: string; in the types, so we use that as key type.\n        elementStyle[key] = style[key];\n    }\n    // Write projection styles directly to element style\n    projection?.applyProjectionStyles(elementStyle, styleProp);\n    for (key in vars) {\n        // Loop over any CSS variables and assign those.\n        // They can only be assigned using `setProperty`.\n        elementStyle.setProperty(key, vars[key]);\n    }\n}\n\nexport { renderHTML };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,OAAO,EAAE;EAAEC,KAAK;EAAEC;AAAK,CAAC,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACjE,MAAMC,YAAY,GAAGL,OAAO,CAACC,KAAK;EAClC,IAAIK,GAAG;EACP,KAAKA,GAAG,IAAIL,KAAK,EAAE;IACf;IACAI,YAAY,CAACC,GAAG,CAAC,GAAGL,KAAK,CAACK,GAAG,CAAC;EAClC;EACA;EACAF,UAAU,EAAEG,qBAAqB,CAACF,YAAY,EAAEF,SAAS,CAAC;EAC1D,KAAKG,GAAG,IAAIJ,IAAI,EAAE;IACd;IACA;IACAG,YAAY,CAACG,WAAW,CAACF,GAAG,EAAEJ,IAAI,CAACI,GAAG,CAAC,CAAC;EAC5C;AACJ;AAEA,SAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}