import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, Download, Github, Linkedin, Mail, ExternalLink, Terminal, Code, Cpu, Zap } from 'lucide-react';

const Hero: React.FC = () => {
  const [typedText, setTypedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  const roles = [
    'AI/ML Engineer',
    'Full-Stack Developer',
    'Automation Expert',
    'Innovation Catalyst'
  ];

  const socialLinks = [
    { icon: Github, href: 'https://github.com/naman2002', label: 'GitHub', color: '#00ffff' },
    { icon: Linkedin, href: 'https://linkedin.com/in/naman-nagi-92026521', label: 'LinkedIn', color: '#0080ff' },
    { icon: ExternalLink, href: 'https://technaman.tech', label: 'Website', color: '#8000ff' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email', color: '#ff0080' },
  ];

  const techIcons = [
    { icon: Terminal, label: 'Terminal' },
    { icon: Code, label: 'Code' },
    { icon: Cpu, label: 'AI/ML' },
    { icon: Zap, label: 'Automation' },
  ];

  // Typewriter effect
  useEffect(() => {
    const currentRole = roles[currentIndex];
    let charIndex = 0;

    const typeInterval = setInterval(() => {
      if (charIndex < currentRole.length) {
        setTypedText(currentRole.slice(0, charIndex + 1));
        charIndex++;
      } else {
        clearInterval(typeInterval);
        setTimeout(() => {
          const deleteInterval = setInterval(() => {
            if (charIndex > 0) {
              setTypedText(currentRole.slice(0, charIndex - 1));
              charIndex--;
            } else {
              clearInterval(deleteInterval);
              setCurrentIndex((prev) => (prev + 1) % roles.length);
            }
          }, 50);
        }, 2000);
      }
    }, 100);

    return () => clearInterval(typeInterval);
  }, [currentIndex, roles]);

  const scrollToAbout = () => {
    const aboutSection = document.querySelector('#about');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-black">
      {/* Cyber Grid Background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
          animation: 'grid-move 20s linear infinite'
        }}></div>
      </div>

      {/* Floating Tech Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {techIcons.map((tech, index) => (
          <motion.div
            key={tech.label}
            className="absolute text-cyan-400/30"
            initial={{ opacity: 0, scale: 0 }}
            animate={{
              opacity: [0.3, 0.7, 0.3],
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              delay: index * 2,
              ease: "easeInOut"
            }}
            style={{
              top: `${20 + index * 20}%`,
              left: `${10 + index * 20}%`,
            }}
          >
            <tech.icon size={40} />
          </motion.div>
        ))}

        {/* Data streams */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-px bg-gradient-to-b from-transparent via-cyan-400 to-transparent"
            style={{
              left: `${15 + i * 15}%`,
              height: '200px',
            }}
            animate={{
              y: ['-200px', '100vh'],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          {/* Terminal-style greeting */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <div className="inline-block bg-black/80 border border-cyan-400/50 rounded-lg p-4 font-mono text-left">
              <div className="flex items-center mb-2">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <span className="ml-4 text-cyan-400 text-sm">terminal@naman-nagi:~$</span>
              </div>
              <motion.p
                initial={{ width: 0 }}
                animate={{ width: "auto" }}
                transition={{ delay: 0.5, duration: 1 }}
                className="text-green-400 text-sm overflow-hidden whitespace-nowrap"
              >
                ./initialize_portfolio.sh --mode=professional
              </motion.p>
            </div>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="space-y-8"
          >
            {/* Name with glitch effect */}
            <motion.h1
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6, duration: 1, type: "spring" }}
              className="text-6xl sm:text-7xl lg:text-9xl font-black gradient-text leading-tight mb-6 glitch"
              data-text="NAMAN NAGI"
            >
              NAMAN NAGI
            </motion.h1>

            {/* Animated role title */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8"
            >
              <span className="text-cyan-400">&gt;</span>{' '}
              <span className="terminal-text">{typedText}</span>
              <span className="animate-pulse text-cyan-400">|</span>
            </motion.div>

            {/* Description with cyber styling */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.8 }}
              className="max-w-4xl mx-auto"
            >
              <div className="neon-card p-8 rounded-2xl scan-lines">
                <p className="text-xl sm:text-2xl text-gray-300 leading-relaxed">
                  Building <span className="text-cyan-400 font-semibold glow-text">energy-efficient</span>,
                  <span className="text-purple-400 font-semibold glow-text"> automated</span>, and
                  <span className="text-pink-400 font-semibold glow-text"> scalable AI solutions</span> with
                  <span className="text-yellow-400 font-bold">2+ years</span> of experience.
                </p>
                <p className="text-lg text-gray-400 mt-4">
                  Passionate about sustainable impact in agriculture, environment monitoring, and smart infrastructure.
                </p>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-8 justify-center items-center mt-12"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={(e) => {
                  e.preventDefault();
                  const contactSection = document.querySelector('#contact');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="btn-cyber glow-border mouse-glow group"
              >
                <Mail size={20} className="mr-2" />
                INITIATE_CONTACT
                <div className="absolute inset-0 bg-cyan-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded"></div>
              </motion.button>

              <motion.a
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                href="/resume.pdf"
                download
                className="btn-cyber btn-cyber-secondary glow-border mouse-glow group"
              >
                <Download size={20} className="mr-2" />
                DOWNLOAD_RESUME
                <div className="absolute inset-0 bg-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded"></div>
              </motion.a>
            </motion.div>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.4, duration: 0.8 }}
              className="flex justify-center space-x-8 mt-12"
            >
              {socialLinks.map((link, index) => (
                <motion.a
                  key={link.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.6 + 0.1 * index, duration: 0.4 }}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative p-4 neon-card rounded-xl mouse-glow transition-all duration-300 hover:scale-110"
                  aria-label={link.label}
                  style={{ '--glow-color': link.color } as React.CSSProperties}
                >
                  <link.icon
                    size={28}
                    className="text-gray-400 group-hover:text-cyan-400 transition-colors duration-300"
                  />
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <span className="text-xs text-cyan-400 font-mono">{link.label}</span>
                  </div>
                </motion.a>
              ))}
            </motion.div>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.8, duration: 0.8 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.button
              onClick={scrollToAbout}
              className="group flex flex-col items-center space-y-2 text-cyan-400 hover:text-white transition-colors duration-300"
              aria-label="Scroll to about section"
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <span className="text-xs font-mono uppercase tracking-wider">Scroll Down</span>
              <div className="w-6 h-10 border-2 border-cyan-400 rounded-full relative">
                <motion.div
                  className="w-1 h-3 bg-cyan-400 rounded-full absolute left-1/2 top-2 transform -translate-x-1/2"
                  animate={{ y: [0, 12, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                />
              </div>
              <ChevronDown size={20} className="animate-bounce" />
            </motion.button>
          </motion.div>
        </div>
      </div>

      {/* Cyber Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Circuit patterns */}
        <motion.div
          className="absolute top-20 left-20 w-32 h-32 border border-cyan-400/20 rounded-lg"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <div className="absolute inset-4 border border-cyan-400/30 rounded"></div>
          <div className="absolute inset-8 border border-cyan-400/40 rounded-full"></div>
        </motion.div>

        <motion.div
          className="absolute bottom-20 right-20 w-24 h-24 border border-purple-400/20 rounded-full"
          animate={{ rotate: -360 }}
          transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
        >
          <div className="absolute inset-2 border border-purple-400/30 rounded-full"></div>
          <div className="absolute inset-4 border border-purple-400/40 rounded-full"></div>
        </motion.div>

        {/* Glowing orbs */}
        <motion.div
          className="absolute top-1/3 right-1/4 w-4 h-4 bg-cyan-400 rounded-full opacity-60"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.6, 1, 0.6],
          }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        />

        <motion.div
          className="absolute bottom-1/3 left-1/4 w-3 h-3 bg-purple-400 rounded-full opacity-60"
          animate={{
            scale: [1, 1.8, 1],
            opacity: [0.6, 1, 0.6],
          }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
      </div>
    </section>
  );
};

export default Hero;
