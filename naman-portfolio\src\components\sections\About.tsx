import React from 'react';
import { motion } from 'framer-motion';
import { Brain, Code, Zap, Target, Users, Award } from 'lucide-react';

const About: React.FC = () => {
  const highlights = [
    {
      icon: Brain,
      title: "AI/ML Expertise",
      description: "Specialized in Generative AI, LLMs, Computer Vision, and NLP with hands-on experience in Stable Diffusion, LoRA, and ControlNet."
    },
    {
      icon: Code,
      title: "Full-Stack Development",
      description: "Proficient in building scalable applications with FastAPI, React, and modern web technologies, with strong backend optimization skills."
    },
    {
      icon: Zap,
      title: "Automation & Efficiency",
      description: "Expert in creating automated systems for HR processes, social media management, and business workflow optimization."
    },
    {
      icon: Target,
      title: "Problem Solver",
      description: "Proven track record in hackathons and competitions, with multiple wins and recognitions for innovative solutions."
    },
    {
      icon: Users,
      title: "Team Leadership",
      description: "Experience in leading teams and coordinating projects, with roles in student organizations and committee leadership."
    },
    {
      icon: Award,
      title: "Continuous Learning",
      description: "Multiple certifications from Google, NVIDIA, AWS, and other leading tech companies, staying updated with latest technologies."
    }
  ];

  const stats = [
    { number: "2+", label: "Years Experience" },
    { number: "15+", label: "Projects Completed" },
    { number: "10+", label: "Certifications" },
    { number: "5+", label: "Hackathon Wins" }
  ];

  return (
    <section id="about" className="py-24 bg-gradient-to-b from-slate-900/50 to-slate-800/30 relative">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold gradient-text mb-4">
            About Me
          </h2>
          <p className="text-xl text-dark-300 max-w-3xl mx-auto">
            Passionate about leveraging AI and technology to create sustainable solutions 
            that make a real-world impact
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Column - Story */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h3 className="text-2xl font-semibold text-white mb-4">
              My Journey in AI & Technology
            </h3>
            
            <div className="space-y-4 text-dark-300 leading-relaxed">
              <p>
                I'm a dynamic AI/ML engineer with over 2 years of experience building energy-efficient, 
                automated, and scalable AI solutions. Currently pursuing my Bachelor's in Computer Science 
                with specialization in AI/ML from Uttaranchal University.
              </p>
              
              <p>
                My expertise spans across machine learning model optimization, green architecture, 
                and backend systems development. I've worked extensively with cutting-edge technologies 
                like Stable Diffusion models, LoRA techniques, and large language models to create 
                innovative solutions.
              </p>
              
              <p>
                At Rapid Innovation, I've built scalable FastAPI backends with real-time AI agent 
                communications, optimized system throughput by 40%, and developed multi-agent systems 
                for contextual conversations. My work focuses on sustainable impact in agriculture, 
                environment monitoring, and smart infrastructure.
              </p>
              
              <p>
                Beyond technical skills, I'm passionate about hackathons and competitions, having won 
                multiple awards including Smart India Hackathon recognitions. I believe in continuous 
                learning and have earned certifications from Google, NVIDIA, AWS, and other leading 
                tech companies.
              </p>
            </div>
          </motion.div>

          {/* Right Column - Stats */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid grid-cols-2 gap-6"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="text-center p-8 glass rounded-2xl hover:border-blue-500/50 transition-all duration-500 card-hover pulse-glow"
              >
                <div className="text-3xl font-bold gradient-text mb-2">
                  {stat.number}
                </div>
                <div className="text-dark-300 text-sm font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Highlights Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {highlights.map((highlight, index) => (
            <motion.div
              key={highlight.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              className="p-8 glass rounded-2xl hover:border-blue-500/50 transition-all duration-500 card-hover group relative overflow-hidden"
            >
              <div className="flex items-center mb-4">
                <div className="p-3 bg-primary-500/10 rounded-lg group-hover:bg-primary-500/20 transition-colors duration-300">
                  <highlight.icon className="w-6 h-6 text-primary-400" />
                </div>
                <h4 className="text-lg font-semibold text-white ml-3">
                  {highlight.title}
                </h4>
              </div>
              <p className="text-dark-300 text-sm leading-relaxed">
                {highlight.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default About;
