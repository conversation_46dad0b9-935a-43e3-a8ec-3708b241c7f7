{"ast": null, "code": "import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { calcChildStagger } from '../utils/calc-child-stagger.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nfunction animateVariant(visualElement, variant, options = {}) {\n  const resolved = resolveVariant(visualElement, variant, options.type === \"exit\" ? visualElement.presenceContext?.custom : undefined);\n  let {\n    transition = visualElement.getDefaultTransition() || {}\n  } = resolved || {};\n  if (options.transitionOverride) {\n    transition = options.transitionOverride;\n  }\n  /**\n   * If we have a variant, create a callback that runs it as an animation.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getAnimation = resolved ? () => Promise.all(animateTarget(visualElement, resolved, options)) : () => Promise.resolve();\n  /**\n   * If we have children, create a callback that runs all their animations.\n   * Otherwise, we resolve a Promise immediately for a composable no-op.\n   */\n  const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size ? (forwardDelay = 0) => {\n    const {\n      delayChildren = 0,\n      staggerChildren,\n      staggerDirection\n    } = transition;\n    return animateChildren(visualElement, variant, forwardDelay, delayChildren, staggerChildren, staggerDirection, options);\n  } : () => Promise.resolve();\n  /**\n   * If the transition explicitly defines a \"when\" option, we need to resolve either\n   * this animation or all children animations before playing the other.\n   */\n  const {\n    when\n  } = transition;\n  if (when) {\n    const [first, last] = when === \"beforeChildren\" ? [getAnimation, getChildAnimations] : [getChildAnimations, getAnimation];\n    return first().then(() => last());\n  } else {\n    return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n  }\n}\nfunction animateChildren(visualElement, variant, delay = 0, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n  const animations = [];\n  for (const child of visualElement.variantChildren) {\n    child.notify(\"AnimationStart\", variant);\n    animations.push(animateVariant(child, variant, {\n      ...options,\n      delay: delay + (typeof delayChildren === \"function\" ? 0 : delayChildren) + calcChildStagger(visualElement.variantChildren, child, delayChildren, staggerChildren, staggerDirection)\n    }).then(() => child.notify(\"AnimationComplete\", variant)));\n  }\n  return Promise.all(animations);\n}\nexport { animateVariant };", "map": {"version": 3, "names": ["resolveV<PERSON>t", "calcChildStagger", "animate<PERSON>arget", "animate<PERSON><PERSON><PERSON>", "visualElement", "variant", "options", "resolved", "type", "presenceContext", "custom", "undefined", "transition", "getDefaultTransition", "transitionOverride", "getAnimation", "Promise", "all", "resolve", "getChildAnimations", "variant<PERSON><PERSON><PERSON>n", "size", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "animate<PERSON><PERSON><PERSON><PERSON>", "when", "first", "last", "then", "delay", "animations", "child", "notify", "push"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs"], "sourcesContent": ["import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { calcChildStagger } from '../utils/calc-child-stagger.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\n\nfunction animateVariant(visualElement, variant, options = {}) {\n    const resolved = resolveVariant(visualElement, variant, options.type === \"exit\"\n        ? visualElement.presenceContext?.custom\n        : undefined);\n    let { transition = visualElement.getDefaultTransition() || {} } = resolved || {};\n    if (options.transitionOverride) {\n        transition = options.transitionOverride;\n    }\n    /**\n     * If we have a variant, create a callback that runs it as an animation.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getAnimation = resolved\n        ? () => Promise.all(animateTarget(visualElement, resolved, options))\n        : () => Promise.resolve();\n    /**\n     * If we have children, create a callback that runs all their animations.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size\n        ? (forwardDelay = 0) => {\n            const { delayChildren = 0, staggerChildren, staggerDirection, } = transition;\n            return animateChildren(visualElement, variant, forwardDelay, delayChildren, staggerChildren, staggerDirection, options);\n        }\n        : () => Promise.resolve();\n    /**\n     * If the transition explicitly defines a \"when\" option, we need to resolve either\n     * this animation or all children animations before playing the other.\n     */\n    const { when } = transition;\n    if (when) {\n        const [first, last] = when === \"beforeChildren\"\n            ? [getAnimation, getChildAnimations]\n            : [getChildAnimations, getAnimation];\n        return first().then(() => last());\n    }\n    else {\n        return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n    }\n}\nfunction animateChildren(visualElement, variant, delay = 0, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n    const animations = [];\n    for (const child of visualElement.variantChildren) {\n        child.notify(\"AnimationStart\", variant);\n        animations.push(animateVariant(child, variant, {\n            ...options,\n            delay: delay +\n                (typeof delayChildren === \"function\" ? 0 : delayChildren) +\n                calcChildStagger(visualElement.variantChildren, child, delayChildren, staggerChildren, staggerDirection),\n        }).then(() => child.notify(\"AnimationComplete\", variant)));\n    }\n    return Promise.all(animations);\n}\n\nexport { animateVariant };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iDAAiD;AAChF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,6BAA6B;AAE3D,SAASC,cAAcA,CAACC,aAAa,EAAEC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC1D,MAAMC,QAAQ,GAAGP,cAAc,CAACI,aAAa,EAAEC,OAAO,EAAEC,OAAO,CAACE,IAAI,KAAK,MAAM,GACzEJ,aAAa,CAACK,eAAe,EAAEC,MAAM,GACrCC,SAAS,CAAC;EAChB,IAAI;IAAEC,UAAU,GAAGR,aAAa,CAACS,oBAAoB,CAAC,CAAC,IAAI,CAAC;EAAE,CAAC,GAAGN,QAAQ,IAAI,CAAC,CAAC;EAChF,IAAID,OAAO,CAACQ,kBAAkB,EAAE;IAC5BF,UAAU,GAAGN,OAAO,CAACQ,kBAAkB;EAC3C;EACA;AACJ;AACA;AACA;EACI,MAAMC,YAAY,GAAGR,QAAQ,GACvB,MAAMS,OAAO,CAACC,GAAG,CAACf,aAAa,CAACE,aAAa,EAAEG,QAAQ,EAAED,OAAO,CAAC,CAAC,GAClE,MAAMU,OAAO,CAACE,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAMC,kBAAkB,GAAGf,aAAa,CAACgB,eAAe,IAAIhB,aAAa,CAACgB,eAAe,CAACC,IAAI,GACxF,CAACC,YAAY,GAAG,CAAC,KAAK;IACpB,MAAM;MAAEC,aAAa,GAAG,CAAC;MAAEC,eAAe;MAAEC;IAAkB,CAAC,GAAGb,UAAU;IAC5E,OAAOc,eAAe,CAACtB,aAAa,EAAEC,OAAO,EAAEiB,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAEnB,OAAO,CAAC;EAC3H,CAAC,GACC,MAAMU,OAAO,CAACE,OAAO,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;EACI,MAAM;IAAES;EAAK,CAAC,GAAGf,UAAU;EAC3B,IAAIe,IAAI,EAAE;IACN,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGF,IAAI,KAAK,gBAAgB,GACzC,CAACZ,YAAY,EAAEI,kBAAkB,CAAC,GAClC,CAACA,kBAAkB,EAAEJ,YAAY,CAAC;IACxC,OAAOa,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,MAAMD,IAAI,CAAC,CAAC,CAAC;EACrC,CAAC,MACI;IACD,OAAOb,OAAO,CAACC,GAAG,CAAC,CAACF,YAAY,CAAC,CAAC,EAAEI,kBAAkB,CAACb,OAAO,CAACyB,KAAK,CAAC,CAAC,CAAC;EAC3E;AACJ;AACA,SAASL,eAAeA,CAACtB,aAAa,EAAEC,OAAO,EAAE0B,KAAK,GAAG,CAAC,EAAER,aAAa,GAAG,CAAC,EAAEC,eAAe,GAAG,CAAC,EAAEC,gBAAgB,GAAG,CAAC,EAAEnB,OAAO,EAAE;EAC/H,MAAM0B,UAAU,GAAG,EAAE;EACrB,KAAK,MAAMC,KAAK,IAAI7B,aAAa,CAACgB,eAAe,EAAE;IAC/Ca,KAAK,CAACC,MAAM,CAAC,gBAAgB,EAAE7B,OAAO,CAAC;IACvC2B,UAAU,CAACG,IAAI,CAAChC,cAAc,CAAC8B,KAAK,EAAE5B,OAAO,EAAE;MAC3C,GAAGC,OAAO;MACVyB,KAAK,EAAEA,KAAK,IACP,OAAOR,aAAa,KAAK,UAAU,GAAG,CAAC,GAAGA,aAAa,CAAC,GACzDtB,gBAAgB,CAACG,aAAa,CAACgB,eAAe,EAAEa,KAAK,EAAEV,aAAa,EAAEC,eAAe,EAAEC,gBAAgB;IAC/G,CAAC,CAAC,CAACK,IAAI,CAAC,MAAMG,KAAK,CAACC,MAAM,CAAC,mBAAmB,EAAE7B,OAAO,CAAC,CAAC,CAAC;EAC9D;EACA,OAAOW,OAAO,CAACC,GAAG,CAACe,UAAU,CAAC;AAClC;AAEA,SAAS7B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}