{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Download, Github, Linkedin, Mail, ExternalLink } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  const socialLinks = [{\n    icon: Github,\n    href: 'https://github.com/naman2002',\n    label: 'GitHub'\n  }, {\n    icon: Linkedin,\n    href: 'https://linkedin.com/in/naman-nagi-92026521',\n    label: 'LinkedIn'\n  }, {\n    icon: ExternalLink,\n    href: 'https://technaman.tech',\n    label: 'Website'\n  }, {\n    icon: Mail,\n    href: 'mailto:<EMAIL>',\n    label: 'Email'\n  }];\n  const scrollToAbout = () => {\n    const aboutSection = document.querySelector('#about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"min-h-screen flex items-center justify-center relative overflow-hidden bg-black\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        style: {\n          backgroundImage: `\n            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px',\n          animation: 'grid-move 20s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [techIcons.map((tech, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute text-cyan-400/30\",\n        initial: {\n          opacity: 0,\n          scale: 0\n        },\n        animate: {\n          opacity: [0.3, 0.7, 0.3],\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 8,\n          repeat: Infinity,\n          delay: index * 2,\n          ease: \"easeInOut\"\n        },\n        style: {\n          top: `${20 + index * 20}%`,\n          left: `${10 + index * 20}%`\n        },\n        children: /*#__PURE__*/_jsxDEV(tech.icon, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this)\n      }, tech.label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this)), [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-px bg-gradient-to-b from-transparent via-cyan-400 to-transparent\",\n        style: {\n          left: `${15 + i * 15}%`,\n          height: '200px'\n        },\n        animate: {\n          y: ['-200px', '100vh'],\n          opacity: [0, 1, 0]\n        },\n        transition: {\n          duration: 3,\n          repeat: Infinity,\n          delay: i * 0.5,\n          ease: \"linear\"\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block bg-black/80 border border-cyan-400/50 rounded-lg p-4 font-mono text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 bg-red-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 bg-yellow-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 bg-green-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-4 text-cyan-400 text-sm\",\n                children: \"terminal@naman-nagi:~$\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: \"auto\"\n              },\n              transition: {\n                delay: 0.5,\n                duration: 1\n              },\n              className: \"text-green-400 text-sm overflow-hidden whitespace-nowrap\",\n              children: \"./initialize_portfolio.sh --mode=professional\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.3\n          },\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.6,\n              duration: 1,\n              type: \"spring\"\n            },\n            className: \"text-6xl sm:text-7xl lg:text-9xl font-black gradient-text leading-tight mb-6 glitch\",\n            \"data-text\": \"NAMAN NAGI\",\n            children: \"NAMAN NAGI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.8,\n              duration: 0.8\n            },\n            className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-cyan-400\",\n              children: \">\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"terminal-text\",\n              children: typedText\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-pulse text-cyan-400\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"neon-card p-8 rounded-2xl scan-lines\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl sm:text-2xl text-gray-300 leading-relaxed\",\n                children: [\"Building \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-cyan-400 font-semibold glow-text\",\n                  children: \"energy-efficient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 28\n                }, this), \",\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-purple-400 font-semibold glow-text\",\n                  children: \" automated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), \", and\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-pink-400 font-semibold glow-text\",\n                  children: \" scalable AI solutions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), \" with\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-400 font-bold\",\n                  children: \"2+ years\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), \" of experience.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-400 mt-4\",\n                children: \"Passionate about sustainable impact in agriculture, environment monitoring, and smart infrastructure.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1.2,\n              duration: 0.8\n            },\n            className: \"flex flex-col sm:flex-row gap-8 justify-center items-center mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: e => {\n                e.preventDefault();\n                const contactSection = document.querySelector('#contact');\n                if (contactSection) {\n                  contactSection.scrollIntoView({\n                    behavior: 'smooth'\n                  });\n                }\n              },\n              className: \"btn-cyber glow-border mouse-glow group\",\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 20,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), \"INITIATE_CONTACT\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-cyan-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              href: \"/resume.pdf\",\n              download: true,\n              className: \"btn-cyber btn-cyber-secondary glow-border mouse-glow group\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 20,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), \"DOWNLOAD_RESUME\", /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              delay: 1.4,\n              duration: 0.8\n            },\n            className: \"flex justify-center space-x-8 mt-12\",\n            children: socialLinks.map((link, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 1.6 + 0.1 * index,\n                duration: 0.4\n              },\n              href: link.href,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"group relative p-4 neon-card rounded-xl mouse-glow transition-all duration-300 hover:scale-110\",\n              \"aria-label\": link.label,\n              style: {\n                '--glow-color': link.color\n              },\n              children: [/*#__PURE__*/_jsxDEV(link.icon, {\n                size: 28,\n                className: \"text-gray-400 group-hover:text-cyan-400 transition-colors duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-cyan-400 font-mono\",\n                  children: link.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)]\n            }, link.label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            delay: 1.8,\n            duration: 0.8\n          },\n          className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n          children: /*#__PURE__*/_jsxDEV(motion.button, {\n            onClick: scrollToAbout,\n            className: \"group flex flex-col items-center space-y-2 text-cyan-400 hover:text-white transition-colors duration-300\",\n            \"aria-label\": \"Scroll to about section\",\n            animate: {\n              y: [0, 10, 0]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-mono uppercase tracking-wider\",\n              children: \"Scroll Down\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-6 h-10 border-2 border-cyan-400 rounded-full relative\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"w-1 h-3 bg-cyan-400 rounded-full absolute left-1/2 top-2 transform -translate-x-1/2\",\n                animate: {\n                  y: [0, 12, 0]\n                },\n                transition: {\n                  duration: 1.5,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n              size: 20,\n              className: \"animate-bounce\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute top-20 left-20 w-32 h-32 border border-cyan-400/20 rounded-lg\",\n        animate: {\n          rotate: 360\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-4 border border-cyan-400/30 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-8 border border-cyan-400/40 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-20 right-20 w-24 h-24 border border-purple-400/20 rounded-full\",\n        animate: {\n          rotate: -360\n        },\n        transition: {\n          duration: 15,\n          repeat: Infinity,\n          ease: \"linear\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-2 border border-purple-400/30 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-4 border border-purple-400/40 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute top-1/3 right-1/4 w-4 h-4 bg-cyan-400 rounded-full opacity-60\",\n        animate: {\n          scale: [1, 1.5, 1],\n          opacity: [0.6, 1, 0.6]\n        },\n        transition: {\n          duration: 3,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-1/3 left-1/4 w-3 h-3 bg-purple-400 rounded-full opacity-60\",\n        animate: {\n          scale: [1, 1.8, 1],\n          opacity: [0.6, 1, 0.6]\n        },\n        transition: {\n          duration: 4,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "motion", "ChevronDown", "Download", "<PERSON><PERSON><PERSON>", "Linkedin", "Mail", "ExternalLink", "jsxDEV", "_jsxDEV", "Hero", "socialLinks", "icon", "href", "label", "scrollToAbout", "aboutSection", "document", "querySelector", "scrollIntoView", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundImage", "backgroundSize", "animation", "techIcons", "map", "tech", "index", "div", "initial", "opacity", "scale", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "delay", "ease", "top", "left", "size", "Array", "_", "i", "height", "y", "p", "width", "h1", "type", "typedText", "button", "whileHover", "whileTap", "onClick", "e", "preventDefault", "contactSection", "a", "download", "link", "target", "rel", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/sections/Hero.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Download, Github, Linkedin, Mail, ExternalLink } from 'lucide-react';\n\nconst Hero: React.FC = () => {\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com/naman2002', label: 'GitHub' },\n    { icon: Linkedin, href: 'https://linkedin.com/in/naman-nagi-92026521', label: 'LinkedIn' },\n    { icon: ExternalLink, href: 'https://technaman.tech', label: 'Website' },\n    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' },\n  ];\n\n  const scrollToAbout = () => {\n    const aboutSection = document.querySelector('#about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-black\">\n      {/* Cyber Grid Background */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10\"></div>\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `\n            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px',\n          animation: 'grid-move 20s linear infinite'\n        }}></div>\n      </div>\n\n      {/* Floating Tech Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {techIcons.map((tech, index) => (\n          <motion.div\n            key={tech.label}\n            className=\"absolute text-cyan-400/30\"\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{\n              opacity: [0.3, 0.7, 0.3],\n              scale: [1, 1.2, 1],\n              rotate: [0, 180, 360]\n            }}\n            transition={{\n              duration: 8,\n              repeat: Infinity,\n              delay: index * 2,\n              ease: \"easeInOut\"\n            }}\n            style={{\n              top: `${20 + index * 20}%`,\n              left: `${10 + index * 20}%`,\n            }}\n          >\n            <tech.icon size={40} />\n          </motion.div>\n        ))}\n\n        {/* Data streams */}\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-px bg-gradient-to-b from-transparent via-cyan-400 to-transparent\"\n            style={{\n              left: `${15 + i * 15}%`,\n              height: '200px',\n            }}\n            animate={{\n              y: ['-200px', '100vh'],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: 3,\n              repeat: Infinity,\n              delay: i * 0.5,\n              ease: \"linear\"\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center\">\n          {/* Terminal-style greeting */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-8\"\n          >\n            <div className=\"inline-block bg-black/80 border border-cyan-400/50 rounded-lg p-4 font-mono text-left\">\n              <div className=\"flex items-center mb-2\">\n                <div className=\"flex space-x-2\">\n                  <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                </div>\n                <span className=\"ml-4 text-cyan-400 text-sm\">terminal@naman-nagi:~$</span>\n              </div>\n              <motion.p\n                initial={{ width: 0 }}\n                animate={{ width: \"auto\" }}\n                transition={{ delay: 0.5, duration: 1 }}\n                className=\"text-green-400 text-sm overflow-hidden whitespace-nowrap\"\n              >\n                ./initialize_portfolio.sh --mode=professional\n              </motion.p>\n            </div>\n          </motion.div>\n\n          {/* Main Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            className=\"space-y-8\"\n          >\n            {/* Name with glitch effect */}\n            <motion.h1\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.6, duration: 1, type: \"spring\" }}\n              className=\"text-6xl sm:text-7xl lg:text-9xl font-black gradient-text leading-tight mb-6 glitch\"\n              data-text=\"NAMAN NAGI\"\n            >\n              NAMAN NAGI\n            </motion.h1>\n\n            {/* Animated role title */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.8 }}\n              className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8\"\n            >\n              <span className=\"text-cyan-400\">&gt;</span>{' '}\n              <span className=\"terminal-text\">{typedText}</span>\n              <span className=\"animate-pulse text-cyan-400\">|</span>\n            </motion.div>\n\n            {/* Description with cyber styling */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.8 }}\n              className=\"max-w-4xl mx-auto\"\n            >\n              <div className=\"neon-card p-8 rounded-2xl scan-lines\">\n                <p className=\"text-xl sm:text-2xl text-gray-300 leading-relaxed\">\n                  Building <span className=\"text-cyan-400 font-semibold glow-text\">energy-efficient</span>,\n                  <span className=\"text-purple-400 font-semibold glow-text\"> automated</span>, and\n                  <span className=\"text-pink-400 font-semibold glow-text\"> scalable AI solutions</span> with\n                  <span className=\"text-yellow-400 font-bold\">2+ years</span> of experience.\n                </p>\n                <p className=\"text-lg text-gray-400 mt-4\">\n                  Passionate about sustainable impact in agriculture, environment monitoring, and smart infrastructure.\n                </p>\n              </div>\n            </motion.div>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1.2, duration: 0.8 }}\n              className=\"flex flex-col sm:flex-row gap-8 justify-center items-center mt-12\"\n            >\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={(e) => {\n                  e.preventDefault();\n                  const contactSection = document.querySelector('#contact');\n                  if (contactSection) {\n                    contactSection.scrollIntoView({ behavior: 'smooth' });\n                  }\n                }}\n                className=\"btn-cyber glow-border mouse-glow group\"\n              >\n                <Mail size={20} className=\"mr-2\" />\n                INITIATE_CONTACT\n                <div className=\"absolute inset-0 bg-cyan-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded\"></div>\n              </motion.button>\n\n              <motion.a\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                href=\"/resume.pdf\"\n                download\n                className=\"btn-cyber btn-cyber-secondary glow-border mouse-glow group\"\n              >\n                <Download size={20} className=\"mr-2\" />\n                DOWNLOAD_RESUME\n                <div className=\"absolute inset-0 bg-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded\"></div>\n              </motion.a>\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 1.4, duration: 0.8 }}\n              className=\"flex justify-center space-x-8 mt-12\"\n            >\n              {socialLinks.map((link, index) => (\n                <motion.a\n                  key={link.label}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.6 + 0.1 * index, duration: 0.4 }}\n                  href={link.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"group relative p-4 neon-card rounded-xl mouse-glow transition-all duration-300 hover:scale-110\"\n                  aria-label={link.label}\n                  style={{ '--glow-color': link.color } as React.CSSProperties}\n                >\n                  <link.icon\n                    size={28}\n                    className=\"text-gray-400 group-hover:text-cyan-400 transition-colors duration-300\"\n                  />\n                  <div className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <span className=\"text-xs text-cyan-400 font-mono\">{link.label}</span>\n                  </div>\n                </motion.a>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Scroll Indicator */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 1.8, duration: 0.8 }}\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          >\n            <motion.button\n              onClick={scrollToAbout}\n              className=\"group flex flex-col items-center space-y-2 text-cyan-400 hover:text-white transition-colors duration-300\"\n              aria-label=\"Scroll to about section\"\n              animate={{ y: [0, 10, 0] }}\n              transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n            >\n              <span className=\"text-xs font-mono uppercase tracking-wider\">Scroll Down</span>\n              <div className=\"w-6 h-10 border-2 border-cyan-400 rounded-full relative\">\n                <motion.div\n                  className=\"w-1 h-3 bg-cyan-400 rounded-full absolute left-1/2 top-2 transform -translate-x-1/2\"\n                  animate={{ y: [0, 12, 0] }}\n                  transition={{ duration: 1.5, repeat: Infinity, ease: \"easeInOut\" }}\n                />\n              </div>\n              <ChevronDown size={20} className=\"animate-bounce\" />\n            </motion.button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Cyber Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {/* Circuit patterns */}\n        <motion.div\n          className=\"absolute top-20 left-20 w-32 h-32 border border-cyan-400/20 rounded-lg\"\n          animate={{ rotate: 360 }}\n          transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n        >\n          <div className=\"absolute inset-4 border border-cyan-400/30 rounded\"></div>\n          <div className=\"absolute inset-8 border border-cyan-400/40 rounded-full\"></div>\n        </motion.div>\n\n        <motion.div\n          className=\"absolute bottom-20 right-20 w-24 h-24 border border-purple-400/20 rounded-full\"\n          animate={{ rotate: -360 }}\n          transition={{ duration: 15, repeat: Infinity, ease: \"linear\" }}\n        >\n          <div className=\"absolute inset-2 border border-purple-400/30 rounded-full\"></div>\n          <div className=\"absolute inset-4 border border-purple-400/40 rounded-full\"></div>\n        </motion.div>\n\n        {/* Glowing orbs */}\n        <motion.div\n          className=\"absolute top-1/3 right-1/4 w-4 h-4 bg-cyan-400 rounded-full opacity-60\"\n          animate={{\n            scale: [1, 1.5, 1],\n            opacity: [0.6, 1, 0.6],\n          }}\n          transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n        />\n\n        <motion.div\n          className=\"absolute bottom-1/3 left-1/4 w-3 h-3 bg-purple-400 rounded-full opacity-60\"\n          animate={{\n            scale: [1, 1.8, 1],\n            opacity: [0.6, 1, 0.6],\n          }}\n          transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\n        />\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3F,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAER,MAAM;IAAES,IAAI,EAAE,8BAA8B;IAAEC,KAAK,EAAE;EAAS,CAAC,EACvE;IAAEF,IAAI,EAAEP,QAAQ;IAAEQ,IAAI,EAAE,6CAA6C;IAAEC,KAAK,EAAE;EAAW,CAAC,EAC1F;IAAEF,IAAI,EAAEL,YAAY;IAAEM,IAAI,EAAE,wBAAwB;IAAEC,KAAK,EAAE;EAAU,CAAC,EACxE;IAAEF,IAAI,EAAEN,IAAI;IAAEO,IAAI,EAAE,gCAAgC;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACvE;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACrD,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACrD;EACF,CAAC;EAED,oBACEX,OAAA;IAASY,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAElGb,OAAA;MAAKY,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1Cb,OAAA;QAAKY,SAAS,EAAC;MAAsF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5GjB,OAAA;QAAKY,SAAS,EAAC,kBAAkB;QAACM,KAAK,EAAE;UACvCC,eAAe,EAAE;AAC3B;AACA;AACA,WAAW;UACDC,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE;QACb;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNjB,OAAA;MAAKY,SAAS,EAAC,kCAAkC;MAAAC,QAAA,GAC9CS,SAAS,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBzB,OAAA,CAACR,MAAM,CAACkC,GAAG;QAETd,SAAS,EAAC,2BAA2B;QACrCe,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCC,OAAO,EAAE;UACPF,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UACxBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,KAAK,EAAEX,KAAK,GAAG,CAAC;UAChBY,IAAI,EAAE;QACR,CAAE;QACFnB,KAAK,EAAE;UACLoB,GAAG,EAAE,GAAG,EAAE,GAAGb,KAAK,GAAG,EAAE,GAAG;UAC1Bc,IAAI,EAAE,GAAG,EAAE,GAAGd,KAAK,GAAG,EAAE;QAC1B,CAAE;QAAAZ,QAAA,eAEFb,OAAA,CAACwB,IAAI,CAACrB,IAAI;UAACqC,IAAI,EAAE;QAAG;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAnBlBO,IAAI,CAACnB,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBL,CACb,CAAC,EAGD,CAAC,GAAGwB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAClB,GAAG,CAAC,CAACmB,CAAC,EAAEC,CAAC,kBACtB3C,OAAA,CAACR,MAAM,CAACkC,GAAG;QAETd,SAAS,EAAC,6EAA6E;QACvFM,KAAK,EAAE;UACLqB,IAAI,EAAE,GAAG,EAAE,GAAGI,CAAC,GAAG,EAAE,GAAG;UACvBC,MAAM,EAAE;QACV,CAAE;QACFd,OAAO,EAAE;UACPe,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;UACtBjB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,CAAE;QACFI,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,KAAK,EAAEO,CAAC,GAAG,GAAG;UACdN,IAAI,EAAE;QACR;MAAE,GAfGM,CAAC;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENjB,OAAA;MAAKY,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEb,OAAA;QAAKY,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAE1Bb,OAAA,CAACR,MAAM,CAACkC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BrB,SAAS,EAAC,MAAM;UAAAC,QAAA,eAEhBb,OAAA;YAAKY,SAAS,EAAC,uFAAuF;YAAAC,QAAA,gBACpGb,OAAA;cAAKY,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCb,OAAA;gBAAKY,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7Bb,OAAA;kBAAKY,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvDjB,OAAA;kBAAKY,SAAS,EAAC;gBAAoC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DjB,OAAA;kBAAKY,SAAS,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNjB,OAAA;gBAAMY,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNjB,OAAA,CAACR,MAAM,CAACsD,CAAC;cACPnB,OAAO,EAAE;gBAAEoB,KAAK,EAAE;cAAE,CAAE;cACtBjB,OAAO,EAAE;gBAAEiB,KAAK,EAAE;cAAO,CAAE;cAC3Bf,UAAU,EAAE;gBAAEI,KAAK,EAAE,GAAG;gBAAEH,QAAQ,EAAE;cAAE,CAAE;cACxCrB,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACrE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbjB,OAAA,CAACR,MAAM,CAACkC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CxB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAGrBb,OAAA,CAACR,MAAM,CAACwD,EAAE;YACRrB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE;YACpCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAE,CAAE;YAClCG,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE,CAAC;cAAEgB,IAAI,EAAE;YAAS,CAAE;YACxDrC,SAAS,EAAC,qFAAqF;YAC/F,aAAU,YAAY;YAAAC,QAAA,EACvB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAGZjB,OAAA,CAACR,MAAM,CAACkC,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CrB,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBAEtEb,OAAA;cAAMY,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAAC,GAAG,eAC/CjB,OAAA;cAAMY,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEqC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDjB,OAAA;cAAMY,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAGbjB,OAAA,CAACR,MAAM,CAACkC,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,CAAC;cAAEH,QAAQ,EAAE;YAAI,CAAE;YACxCrB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAE7Bb,OAAA;cAAKY,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBACnDb,OAAA;gBAAGY,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAC,WACtD,eAAAb,OAAA;kBAAMY,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KACxF,eAAAjB,OAAA;kBAAMY,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,SAC3E,eAAAjB,OAAA;kBAAMY,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,SACrF,eAAAjB,OAAA;kBAAMY,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,mBAC7D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJjB,OAAA;gBAAGY,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbjB,OAAA,CAACR,MAAM,CAACkC,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CrB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAE7Eb,OAAA,CAACR,MAAM,CAAC2D,MAAM;cACZC,UAAU,EAAE;gBAAEvB,KAAK,EAAE;cAAK,CAAE;cAC5BwB,QAAQ,EAAE;gBAAExB,KAAK,EAAE;cAAK,CAAE;cAC1ByB,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClB,MAAMC,cAAc,GAAGjD,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;gBACzD,IAAIgD,cAAc,EAAE;kBAClBA,cAAc,CAAC/C,cAAc,CAAC;oBAAEC,QAAQ,EAAE;kBAAS,CAAC,CAAC;gBACvD;cACF,CAAE;cACFC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBAElDb,OAAA,CAACH,IAAI;gBAAC2C,IAAI,EAAE,EAAG;gBAAC5B,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,oBAEnC,eAAAjB,OAAA;gBAAKY,SAAS,EAAC;cAA2G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpH,CAAC,eAEhBjB,OAAA,CAACR,MAAM,CAACkE,CAAC;cACPN,UAAU,EAAE;gBAAEvB,KAAK,EAAE;cAAK,CAAE;cAC5BwB,QAAQ,EAAE;gBAAExB,KAAK,EAAE;cAAK,CAAE;cAC1BzB,IAAI,EAAC,aAAa;cAClBuD,QAAQ;cACR/C,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBAEtEb,OAAA,CAACN,QAAQ;gBAAC8C,IAAI,EAAE,EAAG;gBAAC5B,SAAS,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEvC,eAAAjB,OAAA;gBAAKY,SAAS,EAAC;cAA6G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGbjB,OAAA,CAACR,MAAM,CAACkC,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBI,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CrB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAE9CX,WAAW,CAACqB,GAAG,CAAC,CAACqC,IAAI,EAAEnC,KAAK,kBAC3BzB,OAAA,CAACR,MAAM,CAACkE,CAAC;cAEP/B,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEiB,CAAC,EAAE;cAAG,CAAE;cAC/Bf,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEiB,CAAC,EAAE;cAAE,CAAE;cAC9Bb,UAAU,EAAE;gBAAEI,KAAK,EAAE,GAAG,GAAG,GAAG,GAAGX,KAAK;gBAAEQ,QAAQ,EAAE;cAAI,CAAE;cACxD7B,IAAI,EAAEwD,IAAI,CAACxD,IAAK;cAChByD,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBlD,SAAS,EAAC,gGAAgG;cAC1G,cAAYgD,IAAI,CAACvD,KAAM;cACvBa,KAAK,EAAE;gBAAE,cAAc,EAAE0C,IAAI,CAACG;cAAM,CAAyB;cAAAlD,QAAA,gBAE7Db,OAAA,CAAC4D,IAAI,CAACzD,IAAI;gBACRqC,IAAI,EAAE,EAAG;gBACT5B,SAAS,EAAC;cAAwE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACFjB,OAAA;gBAAKY,SAAS,EAAC,0HAA0H;gBAAAC,QAAA,eACvIb,OAAA;kBAAMY,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAE+C,IAAI,CAACvD;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA,GAjBD2C,IAAI,CAACvD,KAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBP,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGbjB,OAAA,CAACR,MAAM,CAACkC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBI,UAAU,EAAE;YAAEI,KAAK,EAAE,GAAG;YAAEH,QAAQ,EAAE;UAAI,CAAE;UAC1CrB,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjEb,OAAA,CAACR,MAAM,CAAC2D,MAAM;YACZG,OAAO,EAAEhD,aAAc;YACvBM,SAAS,EAAC,0GAA0G;YACpH,cAAW,yBAAyB;YACpCkB,OAAO,EAAE;cAAEe,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;YAAE,CAAE;YAC3Bb,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEE,IAAI,EAAE;YAAY,CAAE;YAAAxB,QAAA,gBAEjEb,OAAA;cAAMY,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/EjB,OAAA;cAAKY,SAAS,EAAC,yDAAyD;cAAAC,QAAA,eACtEb,OAAA,CAACR,MAAM,CAACkC,GAAG;gBACTd,SAAS,EAAC,qFAAqF;gBAC/FkB,OAAO,EAAE;kBAAEe,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;gBAAE,CAAE;gBAC3Bb,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,MAAM,EAAEC,QAAQ;kBAAEE,IAAI,EAAE;gBAAY;cAAE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjB,OAAA,CAACP,WAAW;cAAC+C,IAAI,EAAE,EAAG;cAAC5B,SAAS,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjB,OAAA;MAAKY,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBAEnEb,OAAA,CAACR,MAAM,CAACkC,GAAG;QACTd,SAAS,EAAC,wEAAwE;QAClFkB,OAAO,EAAE;UAAEC,MAAM,EAAE;QAAI,CAAE;QACzBC,UAAU,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,MAAM,EAAEC,QAAQ;UAAEE,IAAI,EAAE;QAAS,CAAE;QAAAxB,QAAA,gBAE/Db,OAAA;UAAKY,SAAS,EAAC;QAAoD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1EjB,OAAA;UAAKY,SAAS,EAAC;QAAyD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eAEbjB,OAAA,CAACR,MAAM,CAACkC,GAAG;QACTd,SAAS,EAAC,gFAAgF;QAC1FkB,OAAO,EAAE;UAAEC,MAAM,EAAE,CAAC;QAAI,CAAE;QAC1BC,UAAU,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEC,MAAM,EAAEC,QAAQ;UAAEE,IAAI,EAAE;QAAS,CAAE;QAAAxB,QAAA,gBAE/Db,OAAA;UAAKY,SAAS,EAAC;QAA2D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjFjB,OAAA;UAAKY,SAAS,EAAC;QAA2D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eAGbjB,OAAA,CAACR,MAAM,CAACkC,GAAG;QACTd,SAAS,EAAC,wEAAwE;QAClFkB,OAAO,EAAE;UACPD,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBD,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;QACvB,CAAE;QACFI,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEC,MAAM,EAAEC,QAAQ;UAAEE,IAAI,EAAE;QAAY;MAAE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAEFjB,OAAA,CAACR,MAAM,CAACkC,GAAG;QACTd,SAAS,EAAC,4EAA4E;QACtFkB,OAAO,EAAE;UACPD,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBD,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;QACvB,CAAE;QACFI,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEC,MAAM,EAAEC,QAAQ;UAAEE,IAAI,EAAE,WAAW;UAAED,KAAK,EAAE;QAAE;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC+C,EAAA,GA1SI/D,IAAc;AA4SpB,eAAeA,IAAI;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}