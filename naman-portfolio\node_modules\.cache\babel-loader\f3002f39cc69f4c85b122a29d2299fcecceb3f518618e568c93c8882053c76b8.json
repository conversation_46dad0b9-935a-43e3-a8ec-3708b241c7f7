{"ast": null, "code": "import { isCSSVar } from './is-css-var.mjs';\nfunction getComputedStyle(element, name) {\n  const computedStyle = window.getComputedStyle(element);\n  return isCSSVar(name) ? computedStyle.getPropertyValue(name) : computedStyle[name];\n}\nexport { getComputedStyle };", "map": {"version": 3, "names": ["isCSSVar", "getComputedStyle", "element", "name", "computedStyle", "window", "getPropertyValue"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/render/dom/style-computed.mjs"], "sourcesContent": ["import { isCSSVar } from './is-css-var.mjs';\n\nfunction getComputedStyle(element, name) {\n    const computedStyle = window.getComputedStyle(element);\n    return isCSSVar(name)\n        ? computedStyle.getPropertyValue(name)\n        : computedStyle[name];\n}\n\nexport { getComputedStyle };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAE3C,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACrC,MAAMC,aAAa,GAAGC,MAAM,CAACJ,gBAAgB,CAACC,OAAO,CAAC;EACtD,OAAOF,QAAQ,CAACG,IAAI,CAAC,GACfC,aAAa,CAACE,gBAAgB,CAACH,IAAI,CAAC,GACpCC,aAAa,CAACD,IAAI,CAAC;AAC7B;AAEA,SAASF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}