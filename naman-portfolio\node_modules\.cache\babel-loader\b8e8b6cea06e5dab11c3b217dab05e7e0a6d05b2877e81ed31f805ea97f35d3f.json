{"ast": null, "code": "import { cancelFrame, frame } from '../frameloop/frame.mjs';\nfunction subscribeValue(inputValues, outputValue, getLatest) {\n  const update = () => outputValue.set(getLatest());\n  const scheduleUpdate = () => frame.preRender(update, false, true);\n  const subscriptions = inputValues.map(v => v.on(\"change\", scheduleUpdate));\n  outputValue.on(\"destroy\", () => {\n    subscriptions.forEach(unsubscribe => unsubscribe());\n    cancelFrame(update);\n  });\n}\nexport { subscribeValue };", "map": {"version": 3, "names": ["cancelFrame", "frame", "subscribeValue", "inputValues", "outputValue", "getLatest", "update", "set", "scheduleUpdate", "preRender", "subscriptions", "map", "v", "on", "for<PERSON>ach", "unsubscribe"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/value/subscribe-value.mjs"], "sourcesContent": ["import { cancelFrame, frame } from '../frameloop/frame.mjs';\n\nfunction subscribeValue(inputValues, outputValue, getLatest) {\n    const update = () => outputValue.set(getLatest());\n    const scheduleUpdate = () => frame.preRender(update, false, true);\n    const subscriptions = inputValues.map((v) => v.on(\"change\", scheduleUpdate));\n    outputValue.on(\"destroy\", () => {\n        subscriptions.forEach((unsubscribe) => unsubscribe());\n        cancelFrame(update);\n    });\n}\n\nexport { subscribeValue };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,KAAK,QAAQ,wBAAwB;AAE3D,SAASC,cAAcA,CAACC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAE;EACzD,MAAMC,MAAM,GAAGA,CAAA,KAAMF,WAAW,CAACG,GAAG,CAACF,SAAS,CAAC,CAAC,CAAC;EACjD,MAAMG,cAAc,GAAGA,CAAA,KAAMP,KAAK,CAACQ,SAAS,CAACH,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;EACjE,MAAMI,aAAa,GAAGP,WAAW,CAACQ,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,CAAC,QAAQ,EAAEL,cAAc,CAAC,CAAC;EAC5EJ,WAAW,CAACS,EAAE,CAAC,SAAS,EAAE,MAAM;IAC5BH,aAAa,CAACI,OAAO,CAAEC,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC;IACrDf,WAAW,CAACM,MAAM,CAAC;EACvB,CAAC,CAAC;AACN;AAEA,SAASJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}