{"ast": null, "code": "import { motionValue } from './index.mjs';\nimport { JSAnimation } from '../animation/JSAnimation.mjs';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Create a `MotionValue` that animates to its latest value using a spring.\n * Can either be a value or track another `MotionValue`.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction springValue(source, options) {\n  const initialValue = isMotionValue(source) ? source.get() : source;\n  const value = motionValue(initialValue);\n  attachSpring(value, source, options);\n  return value;\n}\nfunction attachSpring(value, source, options) {\n  const initialValue = value.get();\n  let activeAnimation = null;\n  let latestValue = initialValue;\n  let latestSetter;\n  const unit = typeof initialValue === \"string\" ? initialValue.replace(/[\\d.-]/g, \"\") : undefined;\n  const stopAnimation = () => {\n    if (activeAnimation) {\n      activeAnimation.stop();\n      activeAnimation = null;\n    }\n  };\n  const startAnimation = () => {\n    stopAnimation();\n    activeAnimation = new JSAnimation({\n      keyframes: [asNumber(value.get()), asNumber(latestValue)],\n      velocity: value.getVelocity(),\n      type: \"spring\",\n      restDelta: 0.001,\n      restSpeed: 0.01,\n      ...options,\n      onUpdate: latestSetter\n    });\n  };\n  value.attach((v, set) => {\n    latestValue = v;\n    latestSetter = latest => set(parseValue(latest, unit));\n    frame.postRender(startAnimation);\n    return value.get();\n  }, stopAnimation);\n  if (isMotionValue(source)) {\n    const removeSourceOnChange = source.on(\"change\", v => value.set(parseValue(v, unit)));\n    const removeValueOnDestroy = value.on(\"destroy\", removeSourceOnChange);\n    return () => {\n      removeSourceOnChange();\n      removeValueOnDestroy();\n    };\n  }\n  return stopAnimation;\n}\nfunction parseValue(v, unit) {\n  return unit ? v + unit : v;\n}\nfunction asNumber(v) {\n  return typeof v === \"number\" ? v : parseFloat(v);\n}\nexport { attachSpring, springValue };", "map": {"version": 3, "names": ["motionValue", "JSAnimation", "isMotionValue", "frame", "springValue", "source", "options", "initialValue", "get", "value", "attachSpring", "activeAnimation", "latestValue", "latestSetter", "unit", "replace", "undefined", "stopAnimation", "stop", "startAnimation", "keyframes", "asNumber", "velocity", "getVelocity", "type", "restDelta", "restSpeed", "onUpdate", "attach", "v", "set", "latest", "parseValue", "postRender", "removeSourceOnChange", "on", "removeValueOnDestroy", "parseFloat"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/value/spring-value.mjs"], "sourcesContent": ["import { motionValue } from './index.mjs';\nimport { JSAnimation } from '../animation/JSAnimation.mjs';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\nimport { frame } from '../frameloop/frame.mjs';\n\n/**\n * Create a `MotionValue` that animates to its latest value using a spring.\n * Can either be a value or track another `MotionValue`.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction springValue(source, options) {\n    const initialValue = isMotionValue(source) ? source.get() : source;\n    const value = motionValue(initialValue);\n    attachSpring(value, source, options);\n    return value;\n}\nfunction attachSpring(value, source, options) {\n    const initialValue = value.get();\n    let activeAnimation = null;\n    let latestValue = initialValue;\n    let latestSetter;\n    const unit = typeof initialValue === \"string\"\n        ? initialValue.replace(/[\\d.-]/g, \"\")\n        : undefined;\n    const stopAnimation = () => {\n        if (activeAnimation) {\n            activeAnimation.stop();\n            activeAnimation = null;\n        }\n    };\n    const startAnimation = () => {\n        stopAnimation();\n        activeAnimation = new JSAnimation({\n            keyframes: [asNumber(value.get()), asNumber(latestValue)],\n            velocity: value.getVelocity(),\n            type: \"spring\",\n            restDelta: 0.001,\n            restSpeed: 0.01,\n            ...options,\n            onUpdate: latestSetter,\n        });\n    };\n    value.attach((v, set) => {\n        latestValue = v;\n        latestSetter = (latest) => set(parseValue(latest, unit));\n        frame.postRender(startAnimation);\n        return value.get();\n    }, stopAnimation);\n    if (isMotionValue(source)) {\n        const removeSourceOnChange = source.on(\"change\", (v) => value.set(parseValue(v, unit)));\n        const removeValueOnDestroy = value.on(\"destroy\", removeSourceOnChange);\n        return () => {\n            removeSourceOnChange();\n            removeValueOnDestroy();\n        };\n    }\n    return stopAnimation;\n}\nfunction parseValue(v, unit) {\n    return unit ? v + unit : v;\n}\nfunction asNumber(v) {\n    return typeof v === \"number\" ? v : parseFloat(v);\n}\n\nexport { attachSpring, springValue };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,KAAK,QAAQ,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAClC,MAAMC,YAAY,GAAGL,aAAa,CAACG,MAAM,CAAC,GAAGA,MAAM,CAACG,GAAG,CAAC,CAAC,GAAGH,MAAM;EAClE,MAAMI,KAAK,GAAGT,WAAW,CAACO,YAAY,CAAC;EACvCG,YAAY,CAACD,KAAK,EAAEJ,MAAM,EAAEC,OAAO,CAAC;EACpC,OAAOG,KAAK;AAChB;AACA,SAASC,YAAYA,CAACD,KAAK,EAAEJ,MAAM,EAAEC,OAAO,EAAE;EAC1C,MAAMC,YAAY,GAAGE,KAAK,CAACD,GAAG,CAAC,CAAC;EAChC,IAAIG,eAAe,GAAG,IAAI;EAC1B,IAAIC,WAAW,GAAGL,YAAY;EAC9B,IAAIM,YAAY;EAChB,MAAMC,IAAI,GAAG,OAAOP,YAAY,KAAK,QAAQ,GACvCA,YAAY,CAACQ,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GACnCC,SAAS;EACf,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIN,eAAe,EAAE;MACjBA,eAAe,CAACO,IAAI,CAAC,CAAC;MACtBP,eAAe,GAAG,IAAI;IAC1B;EACJ,CAAC;EACD,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IACzBF,aAAa,CAAC,CAAC;IACfN,eAAe,GAAG,IAAIV,WAAW,CAAC;MAC9BmB,SAAS,EAAE,CAACC,QAAQ,CAACZ,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,EAAEa,QAAQ,CAACT,WAAW,CAAC,CAAC;MACzDU,QAAQ,EAAEb,KAAK,CAACc,WAAW,CAAC,CAAC;MAC7BC,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,IAAI;MACf,GAAGpB,OAAO;MACVqB,QAAQ,EAAEd;IACd,CAAC,CAAC;EACN,CAAC;EACDJ,KAAK,CAACmB,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;IACrBlB,WAAW,GAAGiB,CAAC;IACfhB,YAAY,GAAIkB,MAAM,IAAKD,GAAG,CAACE,UAAU,CAACD,MAAM,EAAEjB,IAAI,CAAC,CAAC;IACxDX,KAAK,CAAC8B,UAAU,CAACd,cAAc,CAAC;IAChC,OAAOV,KAAK,CAACD,GAAG,CAAC,CAAC;EACtB,CAAC,EAAES,aAAa,CAAC;EACjB,IAAIf,aAAa,CAACG,MAAM,CAAC,EAAE;IACvB,MAAM6B,oBAAoB,GAAG7B,MAAM,CAAC8B,EAAE,CAAC,QAAQ,EAAGN,CAAC,IAAKpB,KAAK,CAACqB,GAAG,CAACE,UAAU,CAACH,CAAC,EAAEf,IAAI,CAAC,CAAC,CAAC;IACvF,MAAMsB,oBAAoB,GAAG3B,KAAK,CAAC0B,EAAE,CAAC,SAAS,EAAED,oBAAoB,CAAC;IACtE,OAAO,MAAM;MACTA,oBAAoB,CAAC,CAAC;MACtBE,oBAAoB,CAAC,CAAC;IAC1B,CAAC;EACL;EACA,OAAOnB,aAAa;AACxB;AACA,SAASe,UAAUA,CAACH,CAAC,EAAEf,IAAI,EAAE;EACzB,OAAOA,IAAI,GAAGe,CAAC,GAAGf,IAAI,GAAGe,CAAC;AAC9B;AACA,SAASR,QAAQA,CAACQ,CAAC,EAAE;EACjB,OAAO,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGQ,UAAU,CAACR,CAAC,CAAC;AACpD;AAEA,SAASnB,YAAY,EAAEN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}