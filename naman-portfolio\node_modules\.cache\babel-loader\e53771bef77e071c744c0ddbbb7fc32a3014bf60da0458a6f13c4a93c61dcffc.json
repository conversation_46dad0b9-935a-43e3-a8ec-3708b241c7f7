{"ast": null, "code": "/**\n * @license lucide-react v0.540.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 4v6a6 6 0 0 0 12 0V4\",\n  key: \"9kb039\"\n}], [\"line\", {\n  x1: \"4\",\n  x2: \"20\",\n  y1: \"20\",\n  y2: \"20\",\n  key: \"nun2al\"\n}]];\nconst Underline = createLucideIcon(\"underline\", __iconNode);\nexport { __iconNode, Underline as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x1", "x2", "y1", "y2", "Underline", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\node_modules\\lucide-react\\src\\icons\\underline.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 4v6a6 6 0 0 0 12 0V4', key: '9kb039' }],\n  ['line', { x1: '4', x2: '20', y1: '20', y2: '20', key: 'nun2al' }],\n];\n\n/**\n * @component @name Underline\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiA0djZhNiA2IDAgMCAwIDEyIDBWNCIgLz4KICA8bGluZSB4MT0iNCIgeDI9IjIwIiB5MT0iMjAiIHkyPSIyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/underline\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Underline = createLucideIcon('underline', __iconNode);\n\nexport default Underline;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMJ,GAAA,EAAK;AAAA,CAAU,EACnE;AAaA,MAAMK,SAAA,GAAYC,gBAAA,CAAiB,aAAaR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}