{"ast": null, "code": "import { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n  return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n  const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n  const startPress = startEvent => {\n    const target = startEvent.currentTarget;\n    if (!isValidPressEvent(startEvent)) return;\n    isPressing.add(target);\n    const onPressEnd = onPressStart(target, startEvent);\n    const onPointerEnd = (endEvent, success) => {\n      window.removeEventListener(\"pointerup\", onPointerUp);\n      window.removeEventListener(\"pointercancel\", onPointerCancel);\n      if (isPressing.has(target)) {\n        isPressing.delete(target);\n      }\n      if (!isValidPressEvent(endEvent)) {\n        return;\n      }\n      if (typeof onPressEnd === \"function\") {\n        onPressEnd(endEvent, {\n          success\n        });\n      }\n    };\n    const onPointerUp = upEvent => {\n      onPointerEnd(upEvent, target === window || target === document || options.useGlobalTarget || isNodeOrChild(target, upEvent.target));\n    };\n    const onPointerCancel = cancelEvent => {\n      onPointerEnd(cancelEvent, false);\n    };\n    window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n    window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n  };\n  targets.forEach(target => {\n    const pointerDownTarget = options.useGlobalTarget ? window : target;\n    pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n    if (isHTMLElement(target)) {\n      target.addEventListener(\"focus\", event => enableKeyboardPress(event, eventOptions));\n      if (!isElementKeyboardAccessible(target) && !target.hasAttribute(\"tabindex\")) {\n        target.tabIndex = 0;\n      }\n    }\n  });\n  return cancelEvents;\n}\nexport { press };", "map": {"version": 3, "names": ["isHTMLElement", "isDragActive", "isNodeOrChild", "isPrimaryPointer", "setupGesture", "isElementKeyboardAccessible", "enableKeyboardPress", "isPressing", "isValidPressEvent", "event", "press", "targetOrSelector", "onPressStart", "options", "targets", "eventOptions", "cancelEvents", "startPress", "startEvent", "target", "currentTarget", "add", "onPressEnd", "onPointerEnd", "endEvent", "success", "window", "removeEventListener", "onPointerUp", "onPointerCancel", "has", "delete", "upEvent", "document", "useGlobalTarget", "cancelEvent", "addEventListener", "for<PERSON>ach", "pointerDownTarget", "hasAttribute", "tabIndex"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/gestures/press/index.mjs"], "sourcesContent": ["import { isHTMLElement } from '../../utils/is-html-element.mjs';\nimport { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent))\n            return;\n        isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (isPressing.has(target)) {\n                isPressing.delete(target);\n            }\n            if (!isValidPressEvent(endEvent)) {\n                return;\n            }\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                isNodeOrChild(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (isHTMLElement(target)) {\n            target.addEventListener(\"focus\", (event) => enableKeyboardPress(event, eventOptions));\n            if (!isElementKeyboardAccessible(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\nexport { press };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,2BAA2B,QAAQ,oCAAoC;AAChF,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,UAAU,QAAQ,mBAAmB;;AAE9C;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,OAAON,gBAAgB,CAACM,KAAK,CAAC,IAAI,CAACR,YAAY,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,KAAKA,CAACC,gBAAgB,EAAEC,YAAY,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACzD,MAAM,CAACC,OAAO,EAAEC,YAAY,EAAEC,YAAY,CAAC,GAAGZ,YAAY,CAACO,gBAAgB,EAAEE,OAAO,CAAC;EACrF,MAAMI,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,MAAM,GAAGD,UAAU,CAACE,aAAa;IACvC,IAAI,CAACZ,iBAAiB,CAACU,UAAU,CAAC,EAC9B;IACJX,UAAU,CAACc,GAAG,CAACF,MAAM,CAAC;IACtB,MAAMG,UAAU,GAAGV,YAAY,CAACO,MAAM,EAAED,UAAU,CAAC;IACnD,MAAMK,YAAY,GAAGA,CAACC,QAAQ,EAAEC,OAAO,KAAK;MACxCC,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAEC,WAAW,CAAC;MACpDF,MAAM,CAACC,mBAAmB,CAAC,eAAe,EAAEE,eAAe,CAAC;MAC5D,IAAItB,UAAU,CAACuB,GAAG,CAACX,MAAM,CAAC,EAAE;QACxBZ,UAAU,CAACwB,MAAM,CAACZ,MAAM,CAAC;MAC7B;MACA,IAAI,CAACX,iBAAiB,CAACgB,QAAQ,CAAC,EAAE;QAC9B;MACJ;MACA,IAAI,OAAOF,UAAU,KAAK,UAAU,EAAE;QAClCA,UAAU,CAACE,QAAQ,EAAE;UAAEC;QAAQ,CAAC,CAAC;MACrC;IACJ,CAAC;IACD,MAAMG,WAAW,GAAII,OAAO,IAAK;MAC7BT,YAAY,CAACS,OAAO,EAAEb,MAAM,KAAKO,MAAM,IACnCP,MAAM,KAAKc,QAAQ,IACnBpB,OAAO,CAACqB,eAAe,IACvBhC,aAAa,CAACiB,MAAM,EAAEa,OAAO,CAACb,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD,MAAMU,eAAe,GAAIM,WAAW,IAAK;MACrCZ,YAAY,CAACY,WAAW,EAAE,KAAK,CAAC;IACpC,CAAC;IACDT,MAAM,CAACU,gBAAgB,CAAC,WAAW,EAAER,WAAW,EAAEb,YAAY,CAAC;IAC/DW,MAAM,CAACU,gBAAgB,CAAC,eAAe,EAAEP,eAAe,EAAEd,YAAY,CAAC;EAC3E,CAAC;EACDD,OAAO,CAACuB,OAAO,CAAElB,MAAM,IAAK;IACxB,MAAMmB,iBAAiB,GAAGzB,OAAO,CAACqB,eAAe,GAAGR,MAAM,GAAGP,MAAM;IACnEmB,iBAAiB,CAACF,gBAAgB,CAAC,aAAa,EAAEnB,UAAU,EAAEF,YAAY,CAAC;IAC3E,IAAIf,aAAa,CAACmB,MAAM,CAAC,EAAE;MACvBA,MAAM,CAACiB,gBAAgB,CAAC,OAAO,EAAG3B,KAAK,IAAKH,mBAAmB,CAACG,KAAK,EAAEM,YAAY,CAAC,CAAC;MACrF,IAAI,CAACV,2BAA2B,CAACc,MAAM,CAAC,IACpC,CAACA,MAAM,CAACoB,YAAY,CAAC,UAAU,CAAC,EAAE;QAClCpB,MAAM,CAACqB,QAAQ,GAAG,CAAC;MACvB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOxB,YAAY;AACvB;AAEA,SAASN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}