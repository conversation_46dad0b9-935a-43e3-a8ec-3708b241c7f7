{"ast": null, "code": "import { isMotionValue, motionValue } from 'motion-dom';\nfunction updateMotionValuesFromProps(element, next, prev) {\n  for (const key in next) {\n    const nextValue = next[key];\n    const prevValue = prev[key];\n    if (isMotionValue(nextValue)) {\n      /**\n       * If this is a motion value found in props or style, we want to add it\n       * to our visual element's motion value map.\n       */\n      element.addValue(key, nextValue);\n    } else if (isMotionValue(prevValue)) {\n      /**\n       * If we're swapping from a motion value to a static value,\n       * create a new motion value from that\n       */\n      element.addValue(key, motionValue(nextValue, {\n        owner: element\n      }));\n    } else if (prevValue !== nextValue) {\n      /**\n       * If this is a flat value that has changed, update the motion value\n       * or create one if it doesn't exist. We only want to do this if we're\n       * not handling the value with our animation state.\n       */\n      if (element.hasValue(key)) {\n        const existingValue = element.getValue(key);\n        if (existingValue.liveStyle === true) {\n          existingValue.jump(nextValue);\n        } else if (!existingValue.hasAnimated) {\n          existingValue.set(nextValue);\n        }\n      } else {\n        const latestValue = element.getStaticValue(key);\n        element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, {\n          owner: element\n        }));\n      }\n    }\n  }\n  // Handle removed values\n  for (const key in prev) {\n    if (next[key] === undefined) element.removeValue(key);\n  }\n  return next;\n}\nexport { updateMotionValuesFromProps };", "map": {"version": 3, "names": ["isMotionValue", "motionValue", "updateMotionValuesFromProps", "element", "next", "prev", "key", "nextValue", "prevValue", "addValue", "owner", "hasValue", "existingValue", "getValue", "liveStyle", "jump", "hasAnimated", "set", "latestValue", "getStaticValue", "undefined", "removeValue"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs"], "sourcesContent": ["import { isMotionValue, motionValue } from 'motion-dom';\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if (isMotionValue(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n        }\n        else if (isMotionValue(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, motionValue(nextValue, { owner: element }));\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                if (existingValue.liveStyle === true) {\n                    existingValue.jump(nextValue);\n                }\n                else if (!existingValue.hasAnimated) {\n                    existingValue.set(nextValue);\n                }\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\nexport { updateMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,WAAW,QAAQ,YAAY;AAEvD,SAASC,2BAA2BA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtD,KAAK,MAAMC,GAAG,IAAIF,IAAI,EAAE;IACpB,MAAMG,SAAS,GAAGH,IAAI,CAACE,GAAG,CAAC;IAC3B,MAAME,SAAS,GAAGH,IAAI,CAACC,GAAG,CAAC;IAC3B,IAAIN,aAAa,CAACO,SAAS,CAAC,EAAE;MAC1B;AACZ;AACA;AACA;MACYJ,OAAO,CAACM,QAAQ,CAACH,GAAG,EAAEC,SAAS,CAAC;IACpC,CAAC,MACI,IAAIP,aAAa,CAACQ,SAAS,CAAC,EAAE;MAC/B;AACZ;AACA;AACA;MACYL,OAAO,CAACM,QAAQ,CAACH,GAAG,EAAEL,WAAW,CAACM,SAAS,EAAE;QAAEG,KAAK,EAAEP;MAAQ,CAAC,CAAC,CAAC;IACrE,CAAC,MACI,IAAIK,SAAS,KAAKD,SAAS,EAAE;MAC9B;AACZ;AACA;AACA;AACA;MACY,IAAIJ,OAAO,CAACQ,QAAQ,CAACL,GAAG,CAAC,EAAE;QACvB,MAAMM,aAAa,GAAGT,OAAO,CAACU,QAAQ,CAACP,GAAG,CAAC;QAC3C,IAAIM,aAAa,CAACE,SAAS,KAAK,IAAI,EAAE;UAClCF,aAAa,CAACG,IAAI,CAACR,SAAS,CAAC;QACjC,CAAC,MACI,IAAI,CAACK,aAAa,CAACI,WAAW,EAAE;UACjCJ,aAAa,CAACK,GAAG,CAACV,SAAS,CAAC;QAChC;MACJ,CAAC,MACI;QACD,MAAMW,WAAW,GAAGf,OAAO,CAACgB,cAAc,CAACb,GAAG,CAAC;QAC/CH,OAAO,CAACM,QAAQ,CAACH,GAAG,EAAEL,WAAW,CAACiB,WAAW,KAAKE,SAAS,GAAGF,WAAW,GAAGX,SAAS,EAAE;UAAEG,KAAK,EAAEP;QAAQ,CAAC,CAAC,CAAC;MAC/G;IACJ;EACJ;EACA;EACA,KAAK,MAAMG,GAAG,IAAID,IAAI,EAAE;IACpB,IAAID,IAAI,CAACE,GAAG,CAAC,KAAKc,SAAS,EACvBjB,OAAO,CAACkB,WAAW,CAACf,GAAG,CAAC;EAChC;EACA,OAAOF,IAAI;AACf;AAEA,SAASF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}