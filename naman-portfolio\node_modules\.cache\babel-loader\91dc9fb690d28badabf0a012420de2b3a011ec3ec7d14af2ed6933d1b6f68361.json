{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\layout\\\\MatrixRain.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MatrixRain = () => {\n  _s();\n  const canvasRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Matrix characters\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()_+-=[]{}|;:,.<>?';\n    const charArray = chars.split('');\n    const fontSize = 14;\n    const columns = canvas.width / fontSize;\n    const drops = [];\n\n    // Initialize drops\n    for (let i = 0; i < columns; i++) {\n      drops[i] = 1;\n    }\n    const draw = () => {\n      // Black background with slight transparency for trail effect\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Set text properties\n      ctx.fillStyle = '#00ffff';\n      ctx.font = `${fontSize}px 'JetBrains Mono', monospace`;\n\n      // Draw characters\n      for (let i = 0; i < drops.length; i++) {\n        const text = charArray[Math.floor(Math.random() * charArray.length)];\n        const x = i * fontSize;\n        const y = drops[i] * fontSize;\n\n        // Add glow effect\n        ctx.shadowColor = '#00ffff';\n        ctx.shadowBlur = 10;\n        ctx.fillText(text, x, y);\n\n        // Reset drop to top randomly\n        if (y > canvas.height && Math.random() > 0.975) {\n          drops[i] = 0;\n        }\n        drops[i]++;\n      }\n    };\n    const interval = setInterval(draw, 50);\n    return () => {\n      clearInterval(interval);\n      window.removeEventListener('resize', resizeCanvas);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"canvas\", {\n    ref: canvasRef,\n    className: \"fixed top-0 left-0 w-full h-full pointer-events-none z-0 opacity-10\",\n    style: {\n      mixBlendMode: 'screen'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(MatrixRain, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = MatrixRain;\nexport default MatrixRain;\nvar _c;\n$RefreshReg$(_c, \"MatrixRain\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "MatrixRain", "_s", "canvasRef", "canvas", "current", "ctx", "getContext", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "chars", "char<PERSON><PERSON><PERSON>", "split", "fontSize", "columns", "drops", "i", "draw", "fillStyle", "fillRect", "font", "length", "text", "Math", "floor", "random", "x", "y", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "fillText", "interval", "setInterval", "clearInterval", "removeEventListener", "ref", "className", "style", "mixBlendMode", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/layout/MatrixRain.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst MatrixRain: React.FC = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Matrix characters\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()_+-=[]{}|;:,.<>?';\n    const charArray = chars.split('');\n\n    const fontSize = 14;\n    const columns = canvas.width / fontSize;\n    const drops: number[] = [];\n\n    // Initialize drops\n    for (let i = 0; i < columns; i++) {\n      drops[i] = 1;\n    }\n\n    const draw = () => {\n      // Black background with slight transparency for trail effect\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      // Set text properties\n      ctx.fillStyle = '#00ffff';\n      ctx.font = `${fontSize}px 'JetBrains Mono', monospace`;\n\n      // Draw characters\n      for (let i = 0; i < drops.length; i++) {\n        const text = charArray[Math.floor(Math.random() * charArray.length)];\n        const x = i * fontSize;\n        const y = drops[i] * fontSize;\n\n        // Add glow effect\n        ctx.shadowColor = '#00ffff';\n        ctx.shadowBlur = 10;\n        ctx.fillText(text, x, y);\n\n        // Reset drop to top randomly\n        if (y > canvas.height && Math.random() > 0.975) {\n          drops[i] = 0;\n        }\n\n        drops[i]++;\n      }\n    };\n\n    const interval = setInterval(draw, 50);\n\n    return () => {\n      clearInterval(interval);\n      window.removeEventListener('resize', resizeCanvas);\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"fixed top-0 left-0 w-full h-full pointer-events-none z-0 opacity-10\"\n      style={{ mixBlendMode: 'screen' }}\n    />\n  );\n};\n\nexport default MatrixRain;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,SAAS,GAAGL,MAAM,CAAoB,IAAI,CAAC;EAEjDD,SAAS,CAAC,MAAM;IACd,MAAMO,MAAM,GAAGD,SAAS,CAACE,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;;IAEV;IACA,MAAME,YAAY,GAAGA,CAAA,KAAM;MACzBJ,MAAM,CAACK,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCP,MAAM,CAACQ,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,KAAK,GAAG,+DAA+D;IAC7E,MAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,EAAE,CAAC;IAEjC,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,OAAO,GAAGf,MAAM,CAACK,KAAK,GAAGS,QAAQ;IACvC,MAAME,KAAe,GAAG,EAAE;;IAE1B;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,EAAEE,CAAC,EAAE,EAAE;MAChCD,KAAK,CAACC,CAAC,CAAC,GAAG,CAAC;IACd;IAEA,MAAMC,IAAI,GAAGA,CAAA,KAAM;MACjB;MACAhB,GAAG,CAACiB,SAAS,GAAG,qBAAqB;MACrCjB,GAAG,CAACkB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEpB,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACQ,MAAM,CAAC;;MAE/C;MACAN,GAAG,CAACiB,SAAS,GAAG,SAAS;MACzBjB,GAAG,CAACmB,IAAI,GAAG,GAAGP,QAAQ,gCAAgC;;MAEtD;MACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACM,MAAM,EAAEL,CAAC,EAAE,EAAE;QACrC,MAAMM,IAAI,GAAGX,SAAS,CAACY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGd,SAAS,CAACU,MAAM,CAAC,CAAC;QACpE,MAAMK,CAAC,GAAGV,CAAC,GAAGH,QAAQ;QACtB,MAAMc,CAAC,GAAGZ,KAAK,CAACC,CAAC,CAAC,GAAGH,QAAQ;;QAE7B;QACAZ,GAAG,CAAC2B,WAAW,GAAG,SAAS;QAC3B3B,GAAG,CAAC4B,UAAU,GAAG,EAAE;QACnB5B,GAAG,CAAC6B,QAAQ,CAACR,IAAI,EAAEI,CAAC,EAAEC,CAAC,CAAC;;QAExB;QACA,IAAIA,CAAC,GAAG5B,MAAM,CAACQ,MAAM,IAAIgB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,EAAE;UAC9CV,KAAK,CAACC,CAAC,CAAC,GAAG,CAAC;QACd;QAEAD,KAAK,CAACC,CAAC,CAAC,EAAE;MACZ;IACF,CAAC;IAED,MAAMe,QAAQ,GAAGC,WAAW,CAACf,IAAI,EAAE,EAAE,CAAC;IAEtC,OAAO,MAAM;MACXgB,aAAa,CAACF,QAAQ,CAAC;MACvB1B,MAAM,CAAC6B,mBAAmB,CAAC,QAAQ,EAAE/B,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACER,OAAA;IACEwC,GAAG,EAAErC,SAAU;IACfsC,SAAS,EAAC,qEAAqE;IAC/EC,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAS;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnC,CAAC;AAEN,CAAC;AAAC7C,EAAA,CA5EID,UAAoB;AAAA+C,EAAA,GAApB/C,UAAoB;AA8E1B,eAAeA,UAAU;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}