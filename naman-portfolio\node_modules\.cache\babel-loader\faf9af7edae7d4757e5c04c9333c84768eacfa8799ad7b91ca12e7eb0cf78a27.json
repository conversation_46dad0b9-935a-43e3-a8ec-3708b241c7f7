{"ast": null, "code": "import { collectMotionValues } from 'motion-dom';\nimport { useCombineMotionValues } from './use-combine-values.mjs';\nfunction useComputed(compute) {\n  /**\n   * Open session of collectMotionValues. Any MotionValue that calls get()\n   * will be saved into this array.\n   */\n  collectMotionValues.current = [];\n  compute();\n  const value = useCombineMotionValues(collectMotionValues.current, compute);\n  /**\n   * Synchronously close session of collectMotionValues.\n   */\n  collectMotionValues.current = undefined;\n  return value;\n}\nexport { useComputed };", "map": {"version": 3, "names": ["collectMotionValues", "useCombineMotionValues", "useComputed", "compute", "current", "value", "undefined"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/value/use-computed.mjs"], "sourcesContent": ["import { collectMotionValues } from 'motion-dom';\nimport { useCombineMotionValues } from './use-combine-values.mjs';\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    collectMotionValues.current = [];\n    compute();\n    const value = useCombineMotionValues(collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    collectMotionValues.current = undefined;\n    return value;\n}\n\nexport { useComputed };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,YAAY;AAChD,SAASC,sBAAsB,QAAQ,0BAA0B;AAEjE,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC1B;AACJ;AACA;AACA;EACIH,mBAAmB,CAACI,OAAO,GAAG,EAAE;EAChCD,OAAO,CAAC,CAAC;EACT,MAAME,KAAK,GAAGJ,sBAAsB,CAACD,mBAAmB,CAACI,OAAO,EAAED,OAAO,CAAC;EAC1E;AACJ;AACA;EACIH,mBAAmB,CAACI,OAAO,GAAGE,SAAS;EACvC,OAAOD,KAAK;AAChB;AAEA,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}