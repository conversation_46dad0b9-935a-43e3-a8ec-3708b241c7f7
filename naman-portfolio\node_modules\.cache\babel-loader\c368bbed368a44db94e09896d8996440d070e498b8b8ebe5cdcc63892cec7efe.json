{"ast": null, "code": "import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { generateLinearEasing } from '../utils/linear.mjs';\nimport { cubicBezierAsString } from './cubic-bezier.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\nfunction mapEasingToNativeEasing(easing, duration) {\n  if (!easing) {\n    return undefined;\n  } else if (typeof easing === \"function\") {\n    return supportsLinearEasing() ? generateLinearEasing(easing, duration) : \"ease-out\";\n  } else if (isBezierDefinition(easing)) {\n    return cubicBezierAsString(easing);\n  } else if (Array.isArray(easing)) {\n    return easing.map(segmentEasing => mapEasingToNativeEasing(segmentEasing, duration) || supportedWaapiEasing.easeOut);\n  } else {\n    return supportedWaapiEasing[easing];\n  }\n}\nexport { mapEasingToNativeEasing };", "map": {"version": 3, "names": ["isBezierDefinition", "supportsLinearEasing", "generateLinearEasing", "cubicBezierAsString", "supportedWaapiEasing", "mapEasingToNativeEasing", "easing", "duration", "undefined", "Array", "isArray", "map", "segmentEasing", "easeOut"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { generateLinearEasing } from '../utils/linear.mjs';\nimport { cubicBezierAsString } from './cubic-bezier.mjs';\nimport { supportedWaapiEasing } from './supported.mjs';\n\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\") {\n        return supportsLinearEasing()\n            ? generateLinearEasing(easing, duration)\n            : \"ease-out\";\n    }\n    else if (isBezierDefinition(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            supportedWaapiEasing.easeOut);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\nexport { mapEasingToNativeEasing };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,oBAAoB,QAAQ,iBAAiB;AAEtD,SAASC,uBAAuBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC/C,IAAI,CAACD,MAAM,EAAE;IACT,OAAOE,SAAS;EACpB,CAAC,MACI,IAAI,OAAOF,MAAM,KAAK,UAAU,EAAE;IACnC,OAAOL,oBAAoB,CAAC,CAAC,GACvBC,oBAAoB,CAACI,MAAM,EAAEC,QAAQ,CAAC,GACtC,UAAU;EACpB,CAAC,MACI,IAAIP,kBAAkB,CAACM,MAAM,CAAC,EAAE;IACjC,OAAOH,mBAAmB,CAACG,MAAM,CAAC;EACtC,CAAC,MACI,IAAIG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IAC5B,OAAOA,MAAM,CAACK,GAAG,CAAEC,aAAa,IAAKP,uBAAuB,CAACO,aAAa,EAAEL,QAAQ,CAAC,IACjFH,oBAAoB,CAACS,OAAO,CAAC;EACrC,CAAC,MACI;IACD,OAAOT,oBAAoB,CAACE,MAAM,CAAC;EACvC;AACJ;AAEA,SAASD,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}