{"ast": null, "code": "/**\n * @license lucide-react v0.540.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 2v4\",\n  key: \"1cmpym\"\n}], [\"path\", {\n  d: \"M12 2v4\",\n  key: \"3427ic\"\n}], [\"path\", {\n  d: \"M16 2v4\",\n  key: \"4m81vk\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v2\",\n  key: \"j91f56\"\n}], [\"path\", {\n  d: \"M20 12v2\",\n  key: \"w8o0tu\"\n}], [\"path\", {\n  d: \"M20 18v2a2 2 0 0 1-2 2h-1\",\n  key: \"1c9ggx\"\n}], [\"path\", {\n  d: \"M13 22h-2\",\n  key: \"191ugt\"\n}], [\"path\", {\n  d: \"M7 22H6a2 2 0 0 1-2-2v-2\",\n  key: \"1rt9px\"\n}], [\"path\", {\n  d: \"M4 14v-2\",\n  key: \"1v0sqh\"\n}], [\"path\", {\n  d: \"M4 8V6a2 2 0 0 1 2-2h2\",\n  key: \"1mwabg\"\n}], [\"path\", {\n  d: \"M8 10h6\",\n  key: \"3oa6kw\"\n}], [\"path\", {\n  d: \"M8 14h8\",\n  key: \"1fgep2\"\n}], [\"path\", {\n  d: \"M8 18h5\",\n  key: \"17enja\"\n}]];\nconst NotepadTextDashed = createLucideIcon(\"notepad-text-dashed\", __iconNode);\nexport { __iconNode, NotepadTextDashed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "NotepadTextDashed", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\node_modules\\lucide-react\\src\\icons\\notepad-text-dashed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M12 2v4', key: '3427ic' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['path', { d: 'M16 4h2a2 2 0 0 1 2 2v2', key: 'j91f56' }],\n  ['path', { d: 'M20 12v2', key: 'w8o0tu' }],\n  ['path', { d: 'M20 18v2a2 2 0 0 1-2 2h-1', key: '1c9ggx' }],\n  ['path', { d: 'M13 22h-2', key: '191ugt' }],\n  ['path', { d: 'M7 22H6a2 2 0 0 1-2-2v-2', key: '1rt9px' }],\n  ['path', { d: 'M4 14v-2', key: '1v0sqh' }],\n  ['path', { d: 'M4 8V6a2 2 0 0 1 2-2h2', key: '1mwabg' }],\n  ['path', { d: 'M8 10h6', key: '3oa6kw' }],\n  ['path', { d: 'M8 14h8', key: '1fgep2' }],\n  ['path', { d: 'M8 18h5', key: '17enja' }],\n];\n\n/**\n * @component @name NotepadTextDashed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTEyIDJ2NCIgLz4KICA8cGF0aCBkPSJNMTYgMnY0IiAvPgogIDxwYXRoIGQ9Ik0xNiA0aDJhMiAyIDAgMCAxIDIgMnYyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxMnYyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxOHYyYTIgMiAwIDAgMS0yIDJoLTEiIC8+CiAgPHBhdGggZD0iTTEzIDIyaC0yIiAvPgogIDxwYXRoIGQ9Ik03IDIySDZhMiAyIDAgMCAxLTItMnYtMiIgLz4KICA8cGF0aCBkPSJNNCAxNHYtMiIgLz4KICA8cGF0aCBkPSJNNCA4VjZhMiAyIDAgMCAxIDItMmgyIiAvPgogIDxwYXRoIGQ9Ik04IDEwaDYiIC8+CiAgPHBhdGggZD0iTTggMTRoOCIgLz4KICA8cGF0aCBkPSJNOCAxOGg1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/notepad-text-dashed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst NotepadTextDashed = createLucideIcon('notepad-text-dashed', __iconNode);\n\nexport default NotepadTextDashed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAUC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA4BC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA0BC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,EAC1C;AAaA,MAAMC,iBAAA,GAAoBC,gBAAA,CAAiB,uBAAuBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}