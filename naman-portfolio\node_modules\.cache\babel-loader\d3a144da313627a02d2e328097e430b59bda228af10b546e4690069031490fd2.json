{"ast": null, "code": "import { invariant, pipe, clamp, millisecondsToSeconds, secondsToMilliseconds } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { activeAnimations } from '../stats/animation-count.mjs';\nimport { mix } from '../utils/mix/index.mjs';\nimport { frameloopDriver } from './drivers/frame.mjs';\nimport { inertia } from './generators/inertia.mjs';\nimport { keyframes } from './generators/keyframes.mjs';\nimport { calcGeneratorDuration } from './generators/utils/calc-duration.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\nconst percentToProgress = percent => percent / 100;\nclass JSAnimation extends WithPromise {\n  constructor(options) {\n    super();\n    this.state = \"idle\";\n    this.startTime = null;\n    this.isStopped = false;\n    /**\n     * The current time of the animation.\n     */\n    this.currentTime = 0;\n    /**\n     * The time at which the animation was paused.\n     */\n    this.holdTime = null;\n    /**\n     * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n     */\n    this.playbackSpeed = 1;\n    /**\n     * This method is bound to the instance to fix a pattern where\n     * animation.stop is returned as a reference from a useEffect.\n     */\n    this.stop = () => {\n      const {\n        motionValue\n      } = this.options;\n      if (motionValue && motionValue.updatedAt !== time.now()) {\n        this.tick(time.now());\n      }\n      this.isStopped = true;\n      if (this.state === \"idle\") return;\n      this.teardown();\n      this.options.onStop?.();\n    };\n    activeAnimations.mainThread++;\n    this.options = options;\n    this.initAnimation();\n    this.play();\n    if (options.autoplay === false) this.pause();\n  }\n  initAnimation() {\n    const {\n      options\n    } = this;\n    replaceTransitionType(options);\n    const {\n      type = keyframes,\n      repeat = 0,\n      repeatDelay = 0,\n      repeatType,\n      velocity = 0\n    } = options;\n    let {\n      keyframes: keyframes$1\n    } = options;\n    const generatorFactory = type || keyframes;\n    if (process.env.NODE_ENV !== \"production\" && generatorFactory !== keyframes) {\n      invariant(keyframes$1.length <= 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`, \"spring-two-frames\");\n    }\n    if (generatorFactory !== keyframes && typeof keyframes$1[0] !== \"number\") {\n      this.mixKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n      keyframes$1 = [0, 100];\n    }\n    const generator = generatorFactory({\n      ...options,\n      keyframes: keyframes$1\n    });\n    /**\n     * If we have a mirror repeat type we need to create a second generator that outputs the\n     * mirrored (not reversed) animation and later ping pong between the two generators.\n     */\n    if (repeatType === \"mirror\") {\n      this.mirroredGenerator = generatorFactory({\n        ...options,\n        keyframes: [...keyframes$1].reverse(),\n        velocity: -velocity\n      });\n    }\n    /**\n     * If duration is undefined and we have repeat options,\n     * we need to calculate a duration from the generator.\n     *\n     * We set it to the generator itself to cache the duration.\n     * Any timeline resolver will need to have already precalculated\n     * the duration by this step.\n     */\n    if (generator.calculatedDuration === null) {\n      generator.calculatedDuration = calcGeneratorDuration(generator);\n    }\n    const {\n      calculatedDuration\n    } = generator;\n    this.calculatedDuration = calculatedDuration;\n    this.resolvedDuration = calculatedDuration + repeatDelay;\n    this.totalDuration = this.resolvedDuration * (repeat + 1) - repeatDelay;\n    this.generator = generator;\n  }\n  updateTime(timestamp) {\n    const animationTime = Math.round(timestamp - this.startTime) * this.playbackSpeed;\n    // Update currentTime\n    if (this.holdTime !== null) {\n      this.currentTime = this.holdTime;\n    } else {\n      // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n      // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n      // example.\n      this.currentTime = animationTime;\n    }\n  }\n  tick(timestamp, sample = false) {\n    const {\n      generator,\n      totalDuration,\n      mixKeyframes,\n      mirroredGenerator,\n      resolvedDuration,\n      calculatedDuration\n    } = this;\n    if (this.startTime === null) return generator.next(0);\n    const {\n      delay = 0,\n      keyframes,\n      repeat,\n      repeatType,\n      repeatDelay,\n      type,\n      onUpdate,\n      finalKeyframe\n    } = this.options;\n    /**\n     * requestAnimationFrame timestamps can come through as lower than\n     * the startTime as set by performance.now(). Here we prevent this,\n     * though in the future it could be possible to make setting startTime\n     * a pending operation that gets resolved here.\n     */\n    if (this.speed > 0) {\n      this.startTime = Math.min(this.startTime, timestamp);\n    } else if (this.speed < 0) {\n      this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n    }\n    if (sample) {\n      this.currentTime = timestamp;\n    } else {\n      this.updateTime(timestamp);\n    }\n    // Rebase on delay\n    const timeWithoutDelay = this.currentTime - delay * (this.playbackSpeed >= 0 ? 1 : -1);\n    const isInDelayPhase = this.playbackSpeed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n    this.currentTime = Math.max(timeWithoutDelay, 0);\n    // If this animation has finished, set the current time  to the total duration.\n    if (this.state === \"finished\" && this.holdTime === null) {\n      this.currentTime = totalDuration;\n    }\n    let elapsed = this.currentTime;\n    let frameGenerator = generator;\n    if (repeat) {\n      /**\n       * Get the current progress (0-1) of the animation. If t is >\n       * than duration we'll get values like 2.5 (midway through the\n       * third iteration)\n       */\n      const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n      /**\n       * Get the current iteration (0 indexed). For instance the floor of\n       * 2.5 is 2.\n       */\n      let currentIteration = Math.floor(progress);\n      /**\n       * Get the current progress of the iteration by taking the remainder\n       * so 2.5 is 0.5 through iteration 2\n       */\n      let iterationProgress = progress % 1.0;\n      /**\n       * If iteration progress is 1 we count that as the end\n       * of the previous iteration.\n       */\n      if (!iterationProgress && progress >= 1) {\n        iterationProgress = 1;\n      }\n      iterationProgress === 1 && currentIteration--;\n      currentIteration = Math.min(currentIteration, repeat + 1);\n      /**\n       * Reverse progress if we're not running in \"normal\" direction\n       */\n      const isOddIteration = Boolean(currentIteration % 2);\n      if (isOddIteration) {\n        if (repeatType === \"reverse\") {\n          iterationProgress = 1 - iterationProgress;\n          if (repeatDelay) {\n            iterationProgress -= repeatDelay / resolvedDuration;\n          }\n        } else if (repeatType === \"mirror\") {\n          frameGenerator = mirroredGenerator;\n        }\n      }\n      elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n    }\n    /**\n     * If we're in negative time, set state as the initial keyframe.\n     * This prevents delay: x, duration: 0 animations from finishing\n     * instantly.\n     */\n    const state = isInDelayPhase ? {\n      done: false,\n      value: keyframes[0]\n    } : frameGenerator.next(elapsed);\n    if (mixKeyframes) {\n      state.value = mixKeyframes(state.value);\n    }\n    let {\n      done\n    } = state;\n    if (!isInDelayPhase && calculatedDuration !== null) {\n      done = this.playbackSpeed >= 0 ? this.currentTime >= totalDuration : this.currentTime <= 0;\n    }\n    const isAnimationFinished = this.holdTime === null && (this.state === \"finished\" || this.state === \"running\" && done);\n    // TODO: The exception for inertia could be cleaner here\n    if (isAnimationFinished && type !== inertia) {\n      state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n    }\n    if (onUpdate) {\n      onUpdate(state.value);\n    }\n    if (isAnimationFinished) {\n      this.finish();\n    }\n    return state;\n  }\n  /**\n   * Allows the returned animation to be awaited or promise-chained. Currently\n   * resolves when the animation finishes at all but in a future update could/should\n   * reject if its cancels.\n   */\n  then(resolve, reject) {\n    return this.finished.then(resolve, reject);\n  }\n  get duration() {\n    return millisecondsToSeconds(this.calculatedDuration);\n  }\n  get time() {\n    return millisecondsToSeconds(this.currentTime);\n  }\n  set time(newTime) {\n    newTime = secondsToMilliseconds(newTime);\n    this.currentTime = newTime;\n    if (this.startTime === null || this.holdTime !== null || this.playbackSpeed === 0) {\n      this.holdTime = newTime;\n    } else if (this.driver) {\n      this.startTime = this.driver.now() - newTime / this.playbackSpeed;\n    }\n    this.driver?.start(false);\n  }\n  get speed() {\n    return this.playbackSpeed;\n  }\n  set speed(newSpeed) {\n    this.updateTime(time.now());\n    const hasChanged = this.playbackSpeed !== newSpeed;\n    this.playbackSpeed = newSpeed;\n    if (hasChanged) {\n      this.time = millisecondsToSeconds(this.currentTime);\n    }\n  }\n  play() {\n    if (this.isStopped) return;\n    const {\n      driver = frameloopDriver,\n      startTime\n    } = this.options;\n    if (!this.driver) {\n      this.driver = driver(timestamp => this.tick(timestamp));\n    }\n    this.options.onPlay?.();\n    const now = this.driver.now();\n    if (this.state === \"finished\") {\n      this.updateFinished();\n      this.startTime = now;\n    } else if (this.holdTime !== null) {\n      this.startTime = now - this.holdTime;\n    } else if (!this.startTime) {\n      this.startTime = startTime ?? now;\n    }\n    if (this.state === \"finished\" && this.speed < 0) {\n      this.startTime += this.calculatedDuration;\n    }\n    this.holdTime = null;\n    /**\n     * Set playState to running only after we've used it in\n     * the previous logic.\n     */\n    this.state = \"running\";\n    this.driver.start();\n  }\n  pause() {\n    this.state = \"paused\";\n    this.updateTime(time.now());\n    this.holdTime = this.currentTime;\n  }\n  complete() {\n    if (this.state !== \"running\") {\n      this.play();\n    }\n    this.state = \"finished\";\n    this.holdTime = null;\n  }\n  finish() {\n    this.notifyFinished();\n    this.teardown();\n    this.state = \"finished\";\n    this.options.onComplete?.();\n  }\n  cancel() {\n    this.holdTime = null;\n    this.startTime = 0;\n    this.tick(0);\n    this.teardown();\n    this.options.onCancel?.();\n  }\n  teardown() {\n    this.state = \"idle\";\n    this.stopDriver();\n    this.startTime = this.holdTime = null;\n    activeAnimations.mainThread--;\n  }\n  stopDriver() {\n    if (!this.driver) return;\n    this.driver.stop();\n    this.driver = undefined;\n  }\n  sample(sampleTime) {\n    this.startTime = 0;\n    return this.tick(sampleTime, true);\n  }\n  attachTimeline(timeline) {\n    if (this.options.allowFlatten) {\n      this.options.type = \"keyframes\";\n      this.options.ease = \"linear\";\n      this.initAnimation();\n    }\n    this.driver?.stop();\n    return timeline.observe(this);\n  }\n}\n// Legacy function support\nfunction animateValue(options) {\n  return new JSAnimation(options);\n}\nexport { JSAnimation, animateValue };", "map": {"version": 3, "names": ["invariant", "pipe", "clamp", "millisecondsToSeconds", "secondsToMilliseconds", "time", "activeAnimations", "mix", "frameloopDriver", "inertia", "keyframes", "calcGeneratorDuration", "getFinalKeyframe", "replaceTransitionType", "WithPromise", "percentToProgress", "percent", "JSAnimation", "constructor", "options", "state", "startTime", "isStopped", "currentTime", "holdTime", "playbackSpeed", "stop", "motionValue", "updatedAt", "now", "tick", "teardown", "onStop", "mainThread", "initAnimation", "play", "autoplay", "pause", "type", "repeat", "repeatDelay", "repeatType", "velocity", "keyframes$1", "generatorFactory", "process", "env", "NODE_ENV", "length", "mixKeyframes", "generator", "mirroredGenerator", "reverse", "calculatedDuration", "resolvedDuration", "totalDuration", "updateTime", "timestamp", "animationTime", "Math", "round", "sample", "next", "delay", "onUpdate", "finalKeyframe", "speed", "min", "timeWithoutDelay", "isInDelayPhase", "max", "elapsed", "frameGenerator", "progress", "currentIteration", "floor", "iterationProgress", "isOddIteration", "Boolean", "done", "value", "isAnimationFinished", "finish", "then", "resolve", "reject", "finished", "duration", "newTime", "driver", "start", "newSpeed", "has<PERSON><PERSON>ed", "onPlay", "updateFinished", "complete", "notifyFinished", "onComplete", "cancel", "onCancel", "stopDriver", "undefined", "sampleTime", "attachTimeline", "timeline", "allowFlatten", "ease", "observe", "animateValue"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/animation/JSAnimation.mjs"], "sourcesContent": ["import { invariant, pipe, clamp, millisecondsToSeconds, secondsToMilliseconds } from 'motion-utils';\nimport { time } from '../frameloop/sync-time.mjs';\nimport { activeAnimations } from '../stats/animation-count.mjs';\nimport { mix } from '../utils/mix/index.mjs';\nimport { frameloopDriver } from './drivers/frame.mjs';\nimport { inertia } from './generators/inertia.mjs';\nimport { keyframes } from './generators/keyframes.mjs';\nimport { calcGeneratorDuration } from './generators/utils/calc-duration.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { replaceTransitionType } from './utils/replace-transition-type.mjs';\nimport { WithPromise } from './utils/WithPromise.mjs';\n\nconst percentToProgress = (percent) => percent / 100;\nclass JSAnimation extends WithPromise {\n    constructor(options) {\n        super();\n        this.state = \"idle\";\n        this.startTime = null;\n        this.isStopped = false;\n        /**\n         * The current time of the animation.\n         */\n        this.currentTime = 0;\n        /**\n         * The time at which the animation was paused.\n         */\n        this.holdTime = null;\n        /**\n         * Playback speed as a factor. 0 would be stopped, -1 reverse and 2 double speed.\n         */\n        this.playbackSpeed = 1;\n        /**\n         * This method is bound to the instance to fix a pattern where\n         * animation.stop is returned as a reference from a useEffect.\n         */\n        this.stop = () => {\n            const { motionValue } = this.options;\n            if (motionValue && motionValue.updatedAt !== time.now()) {\n                this.tick(time.now());\n            }\n            this.isStopped = true;\n            if (this.state === \"idle\")\n                return;\n            this.teardown();\n            this.options.onStop?.();\n        };\n        activeAnimations.mainThread++;\n        this.options = options;\n        this.initAnimation();\n        this.play();\n        if (options.autoplay === false)\n            this.pause();\n    }\n    initAnimation() {\n        const { options } = this;\n        replaceTransitionType(options);\n        const { type = keyframes, repeat = 0, repeatDelay = 0, repeatType, velocity = 0, } = options;\n        let { keyframes: keyframes$1 } = options;\n        const generatorFactory = type || keyframes;\n        if (process.env.NODE_ENV !== \"production\" &&\n            generatorFactory !== keyframes) {\n            invariant(keyframes$1.length <= 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`, \"spring-two-frames\");\n        }\n        if (generatorFactory !== keyframes &&\n            typeof keyframes$1[0] !== \"number\") {\n            this.mixKeyframes = pipe(percentToProgress, mix(keyframes$1[0], keyframes$1[1]));\n            keyframes$1 = [0, 100];\n        }\n        const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n        /**\n         * If we have a mirror repeat type we need to create a second generator that outputs the\n         * mirrored (not reversed) animation and later ping pong between the two generators.\n         */\n        if (repeatType === \"mirror\") {\n            this.mirroredGenerator = generatorFactory({\n                ...options,\n                keyframes: [...keyframes$1].reverse(),\n                velocity: -velocity,\n            });\n        }\n        /**\n         * If duration is undefined and we have repeat options,\n         * we need to calculate a duration from the generator.\n         *\n         * We set it to the generator itself to cache the duration.\n         * Any timeline resolver will need to have already precalculated\n         * the duration by this step.\n         */\n        if (generator.calculatedDuration === null) {\n            generator.calculatedDuration = calcGeneratorDuration(generator);\n        }\n        const { calculatedDuration } = generator;\n        this.calculatedDuration = calculatedDuration;\n        this.resolvedDuration = calculatedDuration + repeatDelay;\n        this.totalDuration = this.resolvedDuration * (repeat + 1) - repeatDelay;\n        this.generator = generator;\n    }\n    updateTime(timestamp) {\n        const animationTime = Math.round(timestamp - this.startTime) * this.playbackSpeed;\n        // Update currentTime\n        if (this.holdTime !== null) {\n            this.currentTime = this.holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            this.currentTime = animationTime;\n        }\n    }\n    tick(timestamp, sample = false) {\n        const { generator, totalDuration, mixKeyframes, mirroredGenerator, resolvedDuration, calculatedDuration, } = this;\n        if (this.startTime === null)\n            return generator.next(0);\n        const { delay = 0, keyframes, repeat, repeatType, repeatDelay, type, onUpdate, finalKeyframe, } = this.options;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (this.speed > 0) {\n            this.startTime = Math.min(this.startTime, timestamp);\n        }\n        else if (this.speed < 0) {\n            this.startTime = Math.min(timestamp - totalDuration / this.speed, this.startTime);\n        }\n        if (sample) {\n            this.currentTime = timestamp;\n        }\n        else {\n            this.updateTime(timestamp);\n        }\n        // Rebase on delay\n        const timeWithoutDelay = this.currentTime - delay * (this.playbackSpeed >= 0 ? 1 : -1);\n        const isInDelayPhase = this.playbackSpeed >= 0\n            ? timeWithoutDelay < 0\n            : timeWithoutDelay > totalDuration;\n        this.currentTime = Math.max(timeWithoutDelay, 0);\n        // If this animation has finished, set the current time  to the total duration.\n        if (this.state === \"finished\" && this.holdTime === null) {\n            this.currentTime = totalDuration;\n        }\n        let elapsed = this.currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(this.currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes[0] }\n            : frameGenerator.next(elapsed);\n        if (mixKeyframes) {\n            state.value = mixKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done =\n                this.playbackSpeed >= 0\n                    ? this.currentTime >= totalDuration\n                    : this.currentTime <= 0;\n        }\n        const isAnimationFinished = this.holdTime === null &&\n            (this.state === \"finished\" || (this.state === \"running\" && done));\n        // TODO: The exception for inertia could be cleaner here\n        if (isAnimationFinished && type !== inertia) {\n            state.value = getFinalKeyframe(keyframes, this.options, finalKeyframe, this.speed);\n        }\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            this.finish();\n        }\n        return state;\n    }\n    /**\n     * Allows the returned animation to be awaited or promise-chained. Currently\n     * resolves when the animation finishes at all but in a future update could/should\n     * reject if its cancels.\n     */\n    then(resolve, reject) {\n        return this.finished.then(resolve, reject);\n    }\n    get duration() {\n        return millisecondsToSeconds(this.calculatedDuration);\n    }\n    get time() {\n        return millisecondsToSeconds(this.currentTime);\n    }\n    set time(newTime) {\n        newTime = secondsToMilliseconds(newTime);\n        this.currentTime = newTime;\n        if (this.startTime === null ||\n            this.holdTime !== null ||\n            this.playbackSpeed === 0) {\n            this.holdTime = newTime;\n        }\n        else if (this.driver) {\n            this.startTime = this.driver.now() - newTime / this.playbackSpeed;\n        }\n        this.driver?.start(false);\n    }\n    get speed() {\n        return this.playbackSpeed;\n    }\n    set speed(newSpeed) {\n        this.updateTime(time.now());\n        const hasChanged = this.playbackSpeed !== newSpeed;\n        this.playbackSpeed = newSpeed;\n        if (hasChanged) {\n            this.time = millisecondsToSeconds(this.currentTime);\n        }\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        const { driver = frameloopDriver, startTime } = this.options;\n        if (!this.driver) {\n            this.driver = driver((timestamp) => this.tick(timestamp));\n        }\n        this.options.onPlay?.();\n        const now = this.driver.now();\n        if (this.state === \"finished\") {\n            this.updateFinished();\n            this.startTime = now;\n        }\n        else if (this.holdTime !== null) {\n            this.startTime = now - this.holdTime;\n        }\n        else if (!this.startTime) {\n            this.startTime = startTime ?? now;\n        }\n        if (this.state === \"finished\" && this.speed < 0) {\n            this.startTime += this.calculatedDuration;\n        }\n        this.holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        this.state = \"running\";\n        this.driver.start();\n    }\n    pause() {\n        this.state = \"paused\";\n        this.updateTime(time.now());\n        this.holdTime = this.currentTime;\n    }\n    complete() {\n        if (this.state !== \"running\") {\n            this.play();\n        }\n        this.state = \"finished\";\n        this.holdTime = null;\n    }\n    finish() {\n        this.notifyFinished();\n        this.teardown();\n        this.state = \"finished\";\n        this.options.onComplete?.();\n    }\n    cancel() {\n        this.holdTime = null;\n        this.startTime = 0;\n        this.tick(0);\n        this.teardown();\n        this.options.onCancel?.();\n    }\n    teardown() {\n        this.state = \"idle\";\n        this.stopDriver();\n        this.startTime = this.holdTime = null;\n        activeAnimations.mainThread--;\n    }\n    stopDriver() {\n        if (!this.driver)\n            return;\n        this.driver.stop();\n        this.driver = undefined;\n    }\n    sample(sampleTime) {\n        this.startTime = 0;\n        return this.tick(sampleTime, true);\n    }\n    attachTimeline(timeline) {\n        if (this.options.allowFlatten) {\n            this.options.type = \"keyframes\";\n            this.options.ease = \"linear\";\n            this.initAnimation();\n        }\n        this.driver?.stop();\n        return timeline.observe(this);\n    }\n}\n// Legacy function support\nfunction animateValue(options) {\n    return new JSAnimation(options);\n}\n\nexport { JSAnimation, animateValue };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,qBAAqB,QAAQ,cAAc;AACnG,SAASC,IAAI,QAAQ,4BAA4B;AACjD,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,GAAG,QAAQ,wBAAwB;AAC5C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,WAAW,QAAQ,yBAAyB;AAErD,MAAMC,iBAAiB,GAAIC,OAAO,IAAKA,OAAO,GAAG,GAAG;AACpD,MAAMC,WAAW,SAASH,WAAW,CAAC;EAClCI,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,MAAM;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,MAAM;MACd,MAAM;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACR,OAAO;MACpC,IAAIQ,WAAW,IAAIA,WAAW,CAACC,SAAS,KAAKvB,IAAI,CAACwB,GAAG,CAAC,CAAC,EAAE;QACrD,IAAI,CAACC,IAAI,CAACzB,IAAI,CAACwB,GAAG,CAAC,CAAC,CAAC;MACzB;MACA,IAAI,CAACP,SAAS,GAAG,IAAI;MACrB,IAAI,IAAI,CAACF,KAAK,KAAK,MAAM,EACrB;MACJ,IAAI,CAACW,QAAQ,CAAC,CAAC;MACf,IAAI,CAACZ,OAAO,CAACa,MAAM,GAAG,CAAC;IAC3B,CAAC;IACD1B,gBAAgB,CAAC2B,UAAU,EAAE;IAC7B,IAAI,CAACd,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACe,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAIhB,OAAO,CAACiB,QAAQ,KAAK,KAAK,EAC1B,IAAI,CAACC,KAAK,CAAC,CAAC;EACpB;EACAH,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEf;IAAQ,CAAC,GAAG,IAAI;IACxBN,qBAAqB,CAACM,OAAO,CAAC;IAC9B,MAAM;MAAEmB,IAAI,GAAG5B,SAAS;MAAE6B,MAAM,GAAG,CAAC;MAAEC,WAAW,GAAG,CAAC;MAAEC,UAAU;MAAEC,QAAQ,GAAG;IAAG,CAAC,GAAGvB,OAAO;IAC5F,IAAI;MAAET,SAAS,EAAEiC;IAAY,CAAC,GAAGxB,OAAO;IACxC,MAAMyB,gBAAgB,GAAGN,IAAI,IAAI5B,SAAS;IAC1C,IAAImC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCH,gBAAgB,KAAKlC,SAAS,EAAE;MAChCV,SAAS,CAAC2C,WAAW,CAACK,MAAM,IAAI,CAAC,EAAE,gGAAgGL,WAAW,EAAE,EAAE,mBAAmB,CAAC;IAC1K;IACA,IAAIC,gBAAgB,KAAKlC,SAAS,IAC9B,OAAOiC,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACpC,IAAI,CAACM,YAAY,GAAGhD,IAAI,CAACc,iBAAiB,EAAER,GAAG,CAACoC,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;MAChFA,WAAW,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1B;IACA,MAAMO,SAAS,GAAGN,gBAAgB,CAAC;MAAE,GAAGzB,OAAO;MAAET,SAAS,EAAEiC;IAAY,CAAC,CAAC;IAC1E;AACR;AACA;AACA;IACQ,IAAIF,UAAU,KAAK,QAAQ,EAAE;MACzB,IAAI,CAACU,iBAAiB,GAAGP,gBAAgB,CAAC;QACtC,GAAGzB,OAAO;QACVT,SAAS,EAAE,CAAC,GAAGiC,WAAW,CAAC,CAACS,OAAO,CAAC,CAAC;QACrCV,QAAQ,EAAE,CAACA;MACf,CAAC,CAAC;IACN;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIQ,SAAS,CAACG,kBAAkB,KAAK,IAAI,EAAE;MACvCH,SAAS,CAACG,kBAAkB,GAAG1C,qBAAqB,CAACuC,SAAS,CAAC;IACnE;IACA,MAAM;MAAEG;IAAmB,CAAC,GAAGH,SAAS;IACxC,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,gBAAgB,GAAGD,kBAAkB,GAAGb,WAAW;IACxD,IAAI,CAACe,aAAa,GAAG,IAAI,CAACD,gBAAgB,IAAIf,MAAM,GAAG,CAAC,CAAC,GAAGC,WAAW;IACvE,IAAI,CAACU,SAAS,GAAGA,SAAS;EAC9B;EACAM,UAAUA,CAACC,SAAS,EAAE;IAClB,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,GAAG,IAAI,CAACpC,SAAS,CAAC,GAAG,IAAI,CAACI,aAAa;IACjF;IACA,IAAI,IAAI,CAACD,QAAQ,KAAK,IAAI,EAAE;MACxB,IAAI,CAACD,WAAW,GAAG,IAAI,CAACC,QAAQ;IACpC,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAACD,WAAW,GAAGmC,aAAa;IACpC;EACJ;EACA5B,IAAIA,CAAC2B,SAAS,EAAEI,MAAM,GAAG,KAAK,EAAE;IAC5B,MAAM;MAAEX,SAAS;MAAEK,aAAa;MAAEN,YAAY;MAAEE,iBAAiB;MAAEG,gBAAgB;MAAED;IAAoB,CAAC,GAAG,IAAI;IACjH,IAAI,IAAI,CAAChC,SAAS,KAAK,IAAI,EACvB,OAAO6B,SAAS,CAACY,IAAI,CAAC,CAAC,CAAC;IAC5B,MAAM;MAAEC,KAAK,GAAG,CAAC;MAAErD,SAAS;MAAE6B,MAAM;MAAEE,UAAU;MAAED,WAAW;MAAEF,IAAI;MAAE0B,QAAQ;MAAEC;IAAe,CAAC,GAAG,IAAI,CAAC9C,OAAO;IAC9G;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC+C,KAAK,GAAG,CAAC,EAAE;MAChB,IAAI,CAAC7C,SAAS,GAAGsC,IAAI,CAACQ,GAAG,CAAC,IAAI,CAAC9C,SAAS,EAAEoC,SAAS,CAAC;IACxD,CAAC,MACI,IAAI,IAAI,CAACS,KAAK,GAAG,CAAC,EAAE;MACrB,IAAI,CAAC7C,SAAS,GAAGsC,IAAI,CAACQ,GAAG,CAACV,SAAS,GAAGF,aAAa,GAAG,IAAI,CAACW,KAAK,EAAE,IAAI,CAAC7C,SAAS,CAAC;IACrF;IACA,IAAIwC,MAAM,EAAE;MACR,IAAI,CAACtC,WAAW,GAAGkC,SAAS;IAChC,CAAC,MACI;MACD,IAAI,CAACD,UAAU,CAACC,SAAS,CAAC;IAC9B;IACA;IACA,MAAMW,gBAAgB,GAAG,IAAI,CAAC7C,WAAW,GAAGwC,KAAK,IAAI,IAAI,CAACtC,aAAa,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtF,MAAM4C,cAAc,GAAG,IAAI,CAAC5C,aAAa,IAAI,CAAC,GACxC2C,gBAAgB,GAAG,CAAC,GACpBA,gBAAgB,GAAGb,aAAa;IACtC,IAAI,CAAChC,WAAW,GAAGoC,IAAI,CAACW,GAAG,CAACF,gBAAgB,EAAE,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,CAAChD,KAAK,KAAK,UAAU,IAAI,IAAI,CAACI,QAAQ,KAAK,IAAI,EAAE;MACrD,IAAI,CAACD,WAAW,GAAGgC,aAAa;IACpC;IACA,IAAIgB,OAAO,GAAG,IAAI,CAAChD,WAAW;IAC9B,IAAIiD,cAAc,GAAGtB,SAAS;IAC9B,IAAIX,MAAM,EAAE;MACR;AACZ;AACA;AACA;AACA;MACY,MAAMkC,QAAQ,GAAGd,IAAI,CAACQ,GAAG,CAAC,IAAI,CAAC5C,WAAW,EAAEgC,aAAa,CAAC,GAAGD,gBAAgB;MAC7E;AACZ;AACA;AACA;MACY,IAAIoB,gBAAgB,GAAGf,IAAI,CAACgB,KAAK,CAACF,QAAQ,CAAC;MAC3C;AACZ;AACA;AACA;MACY,IAAIG,iBAAiB,GAAGH,QAAQ,GAAG,GAAG;MACtC;AACZ;AACA;AACA;MACY,IAAI,CAACG,iBAAiB,IAAIH,QAAQ,IAAI,CAAC,EAAE;QACrCG,iBAAiB,GAAG,CAAC;MACzB;MACAA,iBAAiB,KAAK,CAAC,IAAIF,gBAAgB,EAAE;MAC7CA,gBAAgB,GAAGf,IAAI,CAACQ,GAAG,CAACO,gBAAgB,EAAEnC,MAAM,GAAG,CAAC,CAAC;MACzD;AACZ;AACA;MACY,MAAMsC,cAAc,GAAGC,OAAO,CAACJ,gBAAgB,GAAG,CAAC,CAAC;MACpD,IAAIG,cAAc,EAAE;QAChB,IAAIpC,UAAU,KAAK,SAAS,EAAE;UAC1BmC,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;UACzC,IAAIpC,WAAW,EAAE;YACboC,iBAAiB,IAAIpC,WAAW,GAAGc,gBAAgB;UACvD;QACJ,CAAC,MACI,IAAIb,UAAU,KAAK,QAAQ,EAAE;UAC9B+B,cAAc,GAAGrB,iBAAiB;QACtC;MACJ;MACAoB,OAAO,GAAGrE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE0E,iBAAiB,CAAC,GAAGtB,gBAAgB;IAC/D;IACA;AACR;AACA;AACA;AACA;IACQ,MAAMlC,KAAK,GAAGiD,cAAc,GACtB;MAAEU,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAEtE,SAAS,CAAC,CAAC;IAAE,CAAC,GACpC8D,cAAc,CAACV,IAAI,CAACS,OAAO,CAAC;IAClC,IAAItB,YAAY,EAAE;MACd7B,KAAK,CAAC4D,KAAK,GAAG/B,YAAY,CAAC7B,KAAK,CAAC4D,KAAK,CAAC;IAC3C;IACA,IAAI;MAAED;IAAK,CAAC,GAAG3D,KAAK;IACpB,IAAI,CAACiD,cAAc,IAAIhB,kBAAkB,KAAK,IAAI,EAAE;MAChD0B,IAAI,GACA,IAAI,CAACtD,aAAa,IAAI,CAAC,GACjB,IAAI,CAACF,WAAW,IAAIgC,aAAa,GACjC,IAAI,CAAChC,WAAW,IAAI,CAAC;IACnC;IACA,MAAM0D,mBAAmB,GAAG,IAAI,CAACzD,QAAQ,KAAK,IAAI,KAC7C,IAAI,CAACJ,KAAK,KAAK,UAAU,IAAK,IAAI,CAACA,KAAK,KAAK,SAAS,IAAI2D,IAAK,CAAC;IACrE;IACA,IAAIE,mBAAmB,IAAI3C,IAAI,KAAK7B,OAAO,EAAE;MACzCW,KAAK,CAAC4D,KAAK,GAAGpE,gBAAgB,CAACF,SAAS,EAAE,IAAI,CAACS,OAAO,EAAE8C,aAAa,EAAE,IAAI,CAACC,KAAK,CAAC;IACtF;IACA,IAAIF,QAAQ,EAAE;MACVA,QAAQ,CAAC5C,KAAK,CAAC4D,KAAK,CAAC;IACzB;IACA,IAAIC,mBAAmB,EAAE;MACrB,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB;IACA,OAAO9D,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACI+D,IAAIA,CAACC,OAAO,EAAEC,MAAM,EAAE;IAClB,OAAO,IAAI,CAACC,QAAQ,CAACH,IAAI,CAACC,OAAO,EAAEC,MAAM,CAAC;EAC9C;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAOpF,qBAAqB,CAAC,IAAI,CAACkD,kBAAkB,CAAC;EACzD;EACA,IAAIhD,IAAIA,CAAA,EAAG;IACP,OAAOF,qBAAqB,CAAC,IAAI,CAACoB,WAAW,CAAC;EAClD;EACA,IAAIlB,IAAIA,CAACmF,OAAO,EAAE;IACdA,OAAO,GAAGpF,qBAAqB,CAACoF,OAAO,CAAC;IACxC,IAAI,CAACjE,WAAW,GAAGiE,OAAO;IAC1B,IAAI,IAAI,CAACnE,SAAS,KAAK,IAAI,IACvB,IAAI,CAACG,QAAQ,KAAK,IAAI,IACtB,IAAI,CAACC,aAAa,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACD,QAAQ,GAAGgE,OAAO;IAC3B,CAAC,MACI,IAAI,IAAI,CAACC,MAAM,EAAE;MAClB,IAAI,CAACpE,SAAS,GAAG,IAAI,CAACoE,MAAM,CAAC5D,GAAG,CAAC,CAAC,GAAG2D,OAAO,GAAG,IAAI,CAAC/D,aAAa;IACrE;IACA,IAAI,CAACgE,MAAM,EAAEC,KAAK,CAAC,KAAK,CAAC;EAC7B;EACA,IAAIxB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACzC,aAAa;EAC7B;EACA,IAAIyC,KAAKA,CAACyB,QAAQ,EAAE;IAChB,IAAI,CAACnC,UAAU,CAACnD,IAAI,CAACwB,GAAG,CAAC,CAAC,CAAC;IAC3B,MAAM+D,UAAU,GAAG,IAAI,CAACnE,aAAa,KAAKkE,QAAQ;IAClD,IAAI,CAAClE,aAAa,GAAGkE,QAAQ;IAC7B,IAAIC,UAAU,EAAE;MACZ,IAAI,CAACvF,IAAI,GAAGF,qBAAqB,CAAC,IAAI,CAACoB,WAAW,CAAC;IACvD;EACJ;EACAY,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACb,SAAS,EACd;IACJ,MAAM;MAAEmE,MAAM,GAAGjF,eAAe;MAAEa;IAAU,CAAC,GAAG,IAAI,CAACF,OAAO;IAC5D,IAAI,CAAC,IAAI,CAACsE,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAEhC,SAAS,IAAK,IAAI,CAAC3B,IAAI,CAAC2B,SAAS,CAAC,CAAC;IAC7D;IACA,IAAI,CAACtC,OAAO,CAAC0E,MAAM,GAAG,CAAC;IACvB,MAAMhE,GAAG,GAAG,IAAI,CAAC4D,MAAM,CAAC5D,GAAG,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACT,KAAK,KAAK,UAAU,EAAE;MAC3B,IAAI,CAAC0E,cAAc,CAAC,CAAC;MACrB,IAAI,CAACzE,SAAS,GAAGQ,GAAG;IACxB,CAAC,MACI,IAAI,IAAI,CAACL,QAAQ,KAAK,IAAI,EAAE;MAC7B,IAAI,CAACH,SAAS,GAAGQ,GAAG,GAAG,IAAI,CAACL,QAAQ;IACxC,CAAC,MACI,IAAI,CAAC,IAAI,CAACH,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAGA,SAAS,IAAIQ,GAAG;IACrC;IACA,IAAI,IAAI,CAACT,KAAK,KAAK,UAAU,IAAI,IAAI,CAAC8C,KAAK,GAAG,CAAC,EAAE;MAC7C,IAAI,CAAC7C,SAAS,IAAI,IAAI,CAACgC,kBAAkB;IAC7C;IACA,IAAI,CAAC7B,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACJ,KAAK,GAAG,SAAS;IACtB,IAAI,CAACqE,MAAM,CAACC,KAAK,CAAC,CAAC;EACvB;EACArD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjB,KAAK,GAAG,QAAQ;IACrB,IAAI,CAACoC,UAAU,CAACnD,IAAI,CAACwB,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACD,WAAW;EACpC;EACAwE,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC3E,KAAK,KAAK,SAAS,EAAE;MAC1B,IAAI,CAACe,IAAI,CAAC,CAAC;IACf;IACA,IAAI,CAACf,KAAK,GAAG,UAAU;IACvB,IAAI,CAACI,QAAQ,GAAG,IAAI;EACxB;EACA0D,MAAMA,CAAA,EAAG;IACL,IAAI,CAACc,cAAc,CAAC,CAAC;IACrB,IAAI,CAACjE,QAAQ,CAAC,CAAC;IACf,IAAI,CAACX,KAAK,GAAG,UAAU;IACvB,IAAI,CAACD,OAAO,CAAC8E,UAAU,GAAG,CAAC;EAC/B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC1E,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACH,SAAS,GAAG,CAAC;IAClB,IAAI,CAACS,IAAI,CAAC,CAAC,CAAC;IACZ,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACZ,OAAO,CAACgF,QAAQ,GAAG,CAAC;EAC7B;EACApE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACX,KAAK,GAAG,MAAM;IACnB,IAAI,CAACgF,UAAU,CAAC,CAAC;IACjB,IAAI,CAAC/E,SAAS,GAAG,IAAI,CAACG,QAAQ,GAAG,IAAI;IACrClB,gBAAgB,CAAC2B,UAAU,EAAE;EACjC;EACAmE,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACX,MAAM,EACZ;IACJ,IAAI,CAACA,MAAM,CAAC/D,IAAI,CAAC,CAAC;IAClB,IAAI,CAAC+D,MAAM,GAAGY,SAAS;EAC3B;EACAxC,MAAMA,CAACyC,UAAU,EAAE;IACf,IAAI,CAACjF,SAAS,GAAG,CAAC;IAClB,OAAO,IAAI,CAACS,IAAI,CAACwE,UAAU,EAAE,IAAI,CAAC;EACtC;EACAC,cAAcA,CAACC,QAAQ,EAAE;IACrB,IAAI,IAAI,CAACrF,OAAO,CAACsF,YAAY,EAAE;MAC3B,IAAI,CAACtF,OAAO,CAACmB,IAAI,GAAG,WAAW;MAC/B,IAAI,CAACnB,OAAO,CAACuF,IAAI,GAAG,QAAQ;MAC5B,IAAI,CAACxE,aAAa,CAAC,CAAC;IACxB;IACA,IAAI,CAACuD,MAAM,EAAE/D,IAAI,CAAC,CAAC;IACnB,OAAO8E,QAAQ,CAACG,OAAO,CAAC,IAAI,CAAC;EACjC;AACJ;AACA;AACA,SAASC,YAAYA,CAACzF,OAAO,EAAE;EAC3B,OAAO,IAAIF,WAAW,CAACE,OAAO,CAAC;AACnC;AAEA,SAASF,WAAW,EAAE2F,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}