{"ast": null, "code": "/**\n * Given a absolute or relative time definition and current/prev time state of the sequence,\n * calculate an absolute time for the next keyframes.\n */\nfunction calcNextTime(current, next, prev, labels) {\n  if (typeof next === \"number\") {\n    return next;\n  } else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n    return Math.max(0, current + parseFloat(next));\n  } else if (next === \"<\") {\n    return prev;\n  } else if (next.startsWith(\"<\")) {\n    return Math.max(0, prev + parseFloat(next.slice(1)));\n  } else {\n    return labels.get(next) ?? current;\n  }\n}\nexport { calcNextTime };", "map": {"version": 3, "names": ["calcNextTime", "current", "next", "prev", "labels", "startsWith", "Math", "max", "parseFloat", "slice", "get"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/animation/sequence/utils/calc-time.mjs"], "sourcesContent": ["/**\n * Given a absolute or relative time definition and current/prev time state of the sequence,\n * calculate an absolute time for the next keyframes.\n */\nfunction calcNextTime(current, next, prev, labels) {\n    if (typeof next === \"number\") {\n        return next;\n    }\n    else if (next.startsWith(\"-\") || next.startsWith(\"+\")) {\n        return Math.max(0, current + parseFloat(next));\n    }\n    else if (next === \"<\") {\n        return prev;\n    }\n    else if (next.startsWith(\"<\")) {\n        return Math.max(0, prev + parseFloat(next.slice(1)));\n    }\n    else {\n        return labels.get(next) ?? current;\n    }\n}\n\nexport { calcNextTime };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC/C,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI;EACf,CAAC,MACI,IAAIA,IAAI,CAACG,UAAU,CAAC,GAAG,CAAC,IAAIH,IAAI,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;IACnD,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,OAAO,GAAGO,UAAU,CAACN,IAAI,CAAC,CAAC;EAClD,CAAC,MACI,IAAIA,IAAI,KAAK,GAAG,EAAE;IACnB,OAAOC,IAAI;EACf,CAAC,MACI,IAAID,IAAI,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;IAC3B,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,IAAI,GAAGK,UAAU,CAACN,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,CAAC,MACI;IACD,OAAOL,MAAM,CAACM,GAAG,CAACR,IAAI,CAAC,IAAID,OAAO;EACtC;AACJ;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}