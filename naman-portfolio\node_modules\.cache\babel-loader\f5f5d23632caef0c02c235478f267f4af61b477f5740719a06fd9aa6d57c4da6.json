{"ast": null, "code": "import { pipe, warning } from 'motion-utils';\nimport { isCSSVariableToken } from '../../animation/utils/is-css-variable.mjs';\nimport { color } from '../../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../../value/types/complex/index.mjs';\nimport { mixColor } from './color.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber as mixNumber$1 } from './number.mjs';\nimport { invisibleValues, mixVisibility } from './visibility.mjs';\nfunction mixNumber(a, b) {\n  return p => mixNumber$1(a, b, p);\n}\nfunction getMixer(a) {\n  if (typeof a === \"number\") {\n    return mixNumber;\n  } else if (typeof a === \"string\") {\n    return isCSSVariableToken(a) ? mixImmediate : color.test(a) ? mixColor : mixComplex;\n  } else if (Array.isArray(a)) {\n    return mixArray;\n  } else if (typeof a === \"object\") {\n    return color.test(a) ? mixColor : mixObject;\n  }\n  return mixImmediate;\n}\nfunction mixArray(a, b) {\n  const output = [...a];\n  const numValues = output.length;\n  const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n  return p => {\n    for (let i = 0; i < numValues; i++) {\n      output[i] = blendValue[i](p);\n    }\n    return output;\n  };\n}\nfunction mixObject(a, b) {\n  const output = {\n    ...a,\n    ...b\n  };\n  const blendValue = {};\n  for (const key in output) {\n    if (a[key] !== undefined && b[key] !== undefined) {\n      blendValue[key] = getMixer(a[key])(a[key], b[key]);\n    }\n  }\n  return v => {\n    for (const key in blendValue) {\n      output[key] = blendValue[key](v);\n    }\n    return output;\n  };\n}\nfunction matchOrder(origin, target) {\n  const orderedOrigin = [];\n  const pointers = {\n    color: 0,\n    var: 0,\n    number: 0\n  };\n  for (let i = 0; i < target.values.length; i++) {\n    const type = target.types[i];\n    const originIndex = origin.indexes[type][pointers[type]];\n    const originValue = origin.values[originIndex] ?? 0;\n    orderedOrigin[i] = originValue;\n    pointers[type]++;\n  }\n  return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n  const template = complex.createTransformer(target);\n  const originStats = analyseComplexValue(origin);\n  const targetStats = analyseComplexValue(target);\n  const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length && originStats.indexes.color.length === targetStats.indexes.color.length && originStats.indexes.number.length >= targetStats.indexes.number.length;\n  if (canInterpolate) {\n    if (invisibleValues.has(origin) && !targetStats.values.length || invisibleValues.has(target) && !originStats.values.length) {\n      return mixVisibility(origin, target);\n    }\n    return pipe(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n  } else {\n    warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`, \"complex-values-different\");\n    return mixImmediate(origin, target);\n  }\n};\nexport { getMixer, mixArray, mixComplex, mixObject };", "map": {"version": 3, "names": ["pipe", "warning", "isCSSVariableToken", "color", "complex", "analyseComplexValue", "mixColor", "mixImmediate", "mixNumber", "mixNumber$1", "invisibleV<PERSON>ues", "mixVisibility", "a", "b", "p", "getMixer", "test", "mixComplex", "Array", "isArray", "mixArray", "mixObject", "output", "numValues", "length", "blendValue", "map", "v", "i", "key", "undefined", "matchOrder", "origin", "target", "<PERSON><PERSON><PERSON><PERSON>", "pointers", "var", "number", "values", "type", "types", "originIndex", "indexes", "originValue", "template", "createTransformer", "originStats", "targetStats", "canInterpolate", "has"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/utils/mix/complex.mjs"], "sourcesContent": ["import { pipe, warning } from 'motion-utils';\nimport { isCSSVariableToken } from '../../animation/utils/is-css-variable.mjs';\nimport { color } from '../../value/types/color/index.mjs';\nimport { complex, analyseComplexValue } from '../../value/types/complex/index.mjs';\nimport { mixColor } from './color.mjs';\nimport { mixImmediate } from './immediate.mjs';\nimport { mixNumber as mixNumber$1 } from './number.mjs';\nimport { invisibleValues, mixVisibility } from './visibility.mjs';\n\nfunction mixNumber(a, b) {\n    return (p) => mixNumber$1(a, b, p);\n}\nfunction getMixer(a) {\n    if (typeof a === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof a === \"string\") {\n        return isCSSVariableToken(a)\n            ? mixImmediate\n            : color.test(a)\n                ? mixColor\n                : mixComplex;\n    }\n    else if (Array.isArray(a)) {\n        return mixArray;\n    }\n    else if (typeof a === \"object\") {\n        return color.test(a) ? mixColor : mixObject;\n    }\n    return mixImmediate;\n}\nfunction mixArray(a, b) {\n    const output = [...a];\n    const numValues = output.length;\n    const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n    return (p) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](p);\n        }\n        return output;\n    };\n}\nfunction mixObject(a, b) {\n    const output = { ...a, ...b };\n    const blendValue = {};\n    for (const key in output) {\n        if (a[key] !== undefined && b[key] !== undefined) {\n            blendValue[key] = getMixer(a[key])(a[key], b[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n}\nfunction matchOrder(origin, target) {\n    const orderedOrigin = [];\n    const pointers = { color: 0, var: 0, number: 0 };\n    for (let i = 0; i < target.values.length; i++) {\n        const type = target.types[i];\n        const originIndex = origin.indexes[type][pointers[type]];\n        const originValue = origin.values[originIndex] ?? 0;\n        orderedOrigin[i] = originValue;\n        pointers[type]++;\n    }\n    return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n    const template = complex.createTransformer(target);\n    const originStats = analyseComplexValue(origin);\n    const targetStats = analyseComplexValue(target);\n    const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length &&\n        originStats.indexes.color.length === targetStats.indexes.color.length &&\n        originStats.indexes.number.length >= targetStats.indexes.number.length;\n    if (canInterpolate) {\n        if ((invisibleValues.has(origin) &&\n            !targetStats.values.length) ||\n            (invisibleValues.has(target) &&\n                !originStats.values.length)) {\n            return mixVisibility(origin, target);\n        }\n        return pipe(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n    }\n    else {\n        warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`, \"complex-values-different\");\n        return mixImmediate(origin, target);\n    }\n};\n\nexport { getMixer, mixArray, mixComplex, mixObject };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,cAAc;AAC5C,SAASC,kBAAkB,QAAQ,2CAA2C;AAC9E,SAASC,KAAK,QAAQ,mCAAmC;AACzD,SAASC,OAAO,EAAEC,mBAAmB,QAAQ,qCAAqC;AAClF,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,IAAIC,WAAW,QAAQ,cAAc;AACvD,SAASC,eAAe,EAAEC,aAAa,QAAQ,kBAAkB;AAEjE,SAASH,SAASA,CAACI,CAAC,EAAEC,CAAC,EAAE;EACrB,OAAQC,CAAC,IAAKL,WAAW,CAACG,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACtC;AACA,SAASC,QAAQA,CAACH,CAAC,EAAE;EACjB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACvB,OAAOJ,SAAS;EACpB,CAAC,MACI,IAAI,OAAOI,CAAC,KAAK,QAAQ,EAAE;IAC5B,OAAOV,kBAAkB,CAACU,CAAC,CAAC,GACtBL,YAAY,GACZJ,KAAK,CAACa,IAAI,CAACJ,CAAC,CAAC,GACTN,QAAQ,GACRW,UAAU;EACxB,CAAC,MACI,IAAIC,KAAK,CAACC,OAAO,CAACP,CAAC,CAAC,EAAE;IACvB,OAAOQ,QAAQ;EACnB,CAAC,MACI,IAAI,OAAOR,CAAC,KAAK,QAAQ,EAAE;IAC5B,OAAOT,KAAK,CAACa,IAAI,CAACJ,CAAC,CAAC,GAAGN,QAAQ,GAAGe,SAAS;EAC/C;EACA,OAAOd,YAAY;AACvB;AACA,SAASa,QAAQA,CAACR,CAAC,EAAEC,CAAC,EAAE;EACpB,MAAMS,MAAM,GAAG,CAAC,GAAGV,CAAC,CAAC;EACrB,MAAMW,SAAS,GAAGD,MAAM,CAACE,MAAM;EAC/B,MAAMC,UAAU,GAAGb,CAAC,CAACc,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKb,QAAQ,CAACY,CAAC,CAAC,CAACA,CAAC,EAAEd,CAAC,CAACe,CAAC,CAAC,CAAC,CAAC;EACxD,OAAQd,CAAC,IAAK;IACV,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;MAChCN,MAAM,CAACM,CAAC,CAAC,GAAGH,UAAU,CAACG,CAAC,CAAC,CAACd,CAAC,CAAC;IAChC;IACA,OAAOQ,MAAM;EACjB,CAAC;AACL;AACA,SAASD,SAASA,CAACT,CAAC,EAAEC,CAAC,EAAE;EACrB,MAAMS,MAAM,GAAG;IAAE,GAAGV,CAAC;IAAE,GAAGC;EAAE,CAAC;EAC7B,MAAMY,UAAU,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMI,GAAG,IAAIP,MAAM,EAAE;IACtB,IAAIV,CAAC,CAACiB,GAAG,CAAC,KAAKC,SAAS,IAAIjB,CAAC,CAACgB,GAAG,CAAC,KAAKC,SAAS,EAAE;MAC9CL,UAAU,CAACI,GAAG,CAAC,GAAGd,QAAQ,CAACH,CAAC,CAACiB,GAAG,CAAC,CAAC,CAACjB,CAAC,CAACiB,GAAG,CAAC,EAAEhB,CAAC,CAACgB,GAAG,CAAC,CAAC;IACtD;EACJ;EACA,OAAQF,CAAC,IAAK;IACV,KAAK,MAAME,GAAG,IAAIJ,UAAU,EAAE;MAC1BH,MAAM,CAACO,GAAG,CAAC,GAAGJ,UAAU,CAACI,GAAG,CAAC,CAACF,CAAC,CAAC;IACpC;IACA,OAAOL,MAAM;EACjB,CAAC;AACL;AACA,SAASS,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAChC,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,QAAQ,GAAG;IAAEhC,KAAK,EAAE,CAAC;IAAEiC,GAAG,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAChD,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,CAACK,MAAM,CAACd,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC3C,MAAMW,IAAI,GAAGN,MAAM,CAACO,KAAK,CAACZ,CAAC,CAAC;IAC5B,MAAMa,WAAW,GAAGT,MAAM,CAACU,OAAO,CAACH,IAAI,CAAC,CAACJ,QAAQ,CAACI,IAAI,CAAC,CAAC;IACxD,MAAMI,WAAW,GAAGX,MAAM,CAACM,MAAM,CAACG,WAAW,CAAC,IAAI,CAAC;IACnDP,aAAa,CAACN,CAAC,CAAC,GAAGe,WAAW;IAC9BR,QAAQ,CAACI,IAAI,CAAC,EAAE;EACpB;EACA,OAAOL,aAAa;AACxB;AACA,MAAMjB,UAAU,GAAGA,CAACe,MAAM,EAAEC,MAAM,KAAK;EACnC,MAAMW,QAAQ,GAAGxC,OAAO,CAACyC,iBAAiB,CAACZ,MAAM,CAAC;EAClD,MAAMa,WAAW,GAAGzC,mBAAmB,CAAC2B,MAAM,CAAC;EAC/C,MAAMe,WAAW,GAAG1C,mBAAmB,CAAC4B,MAAM,CAAC;EAC/C,MAAMe,cAAc,GAAGF,WAAW,CAACJ,OAAO,CAACN,GAAG,CAACZ,MAAM,KAAKuB,WAAW,CAACL,OAAO,CAACN,GAAG,CAACZ,MAAM,IACpFsB,WAAW,CAACJ,OAAO,CAACvC,KAAK,CAACqB,MAAM,KAAKuB,WAAW,CAACL,OAAO,CAACvC,KAAK,CAACqB,MAAM,IACrEsB,WAAW,CAACJ,OAAO,CAACL,MAAM,CAACb,MAAM,IAAIuB,WAAW,CAACL,OAAO,CAACL,MAAM,CAACb,MAAM;EAC1E,IAAIwB,cAAc,EAAE;IAChB,IAAKtC,eAAe,CAACuC,GAAG,CAACjB,MAAM,CAAC,IAC5B,CAACe,WAAW,CAACT,MAAM,CAACd,MAAM,IACzBd,eAAe,CAACuC,GAAG,CAAChB,MAAM,CAAC,IACxB,CAACa,WAAW,CAACR,MAAM,CAACd,MAAO,EAAE;MACjC,OAAOb,aAAa,CAACqB,MAAM,EAAEC,MAAM,CAAC;IACxC;IACA,OAAOjC,IAAI,CAACoB,QAAQ,CAACW,UAAU,CAACe,WAAW,EAAEC,WAAW,CAAC,EAAEA,WAAW,CAACT,MAAM,CAAC,EAAEM,QAAQ,CAAC;EAC7F,CAAC,MACI;IACD3C,OAAO,CAAC,IAAI,EAAE,mBAAmB+B,MAAM,UAAUC,MAAM,0KAA0K,EAAE,0BAA0B,CAAC;IAC9P,OAAO1B,YAAY,CAACyB,MAAM,EAAEC,MAAM,CAAC;EACvC;AACJ,CAAC;AAED,SAASlB,QAAQ,EAAEK,QAAQ,EAAEH,UAAU,EAAEI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}