{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\sections\\\\Achievements.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Trophy, Medal, Award, Users, Calendar, Building } from 'lucide-react';\nimport { achievements } from '../../data/portfolio';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Achievements = () => {\n  _s();\n  const [activeFilter, setActiveFilter] = useState('All');\n  const categories = [{\n    name: 'All',\n    icon: Trophy\n  }, {\n    name: 'Hackathon',\n    icon: Trophy\n  }, {\n    name: 'Competition',\n    icon: Medal\n  }, {\n    name: 'Award',\n    icon: Award\n  }, {\n    name: 'Recognition',\n    icon: Users\n  }];\n  const filteredAchievements = activeFilter === 'All' ? achievements : achievements.filter(achievement => achievement.category === activeFilter);\n  const getCategoryIcon = category => {\n    switch (category) {\n      case 'Hackathon':\n        return Trophy;\n      case 'Competition':\n        return Medal;\n      case 'Award':\n        return Award;\n      case 'Recognition':\n        return Users;\n      default:\n        return Trophy;\n    }\n  };\n  const getCategoryColor = category => {\n    switch (category) {\n      case 'Hackathon':\n        return 'text-accent-400 bg-accent-500/10 border-accent-500/20';\n      case 'Competition':\n        return 'text-primary-400 bg-primary-500/10 border-primary-500/20';\n      case 'Award':\n        return 'text-secondary-400 bg-secondary-500/10 border-secondary-500/20';\n      case 'Recognition':\n        return 'text-purple-400 bg-purple-500/10 border-purple-500/20';\n      default:\n        return 'text-dark-400 bg-dark-500/10 border-dark-500/20';\n    }\n  };\n  const getPositionColor = position => {\n    if (!position) return '';\n    if (position.includes('1st') || position.includes('Winner') || position.includes('Top 10')) {\n      return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';\n    }\n    if (position.includes('2nd') || position.includes('Runner')) {\n      return 'text-gray-300 bg-gray-500/10 border-gray-500/20';\n    }\n    if (position.includes('3rd') || position.includes('Consolation')) {\n      return 'text-orange-400 bg-orange-500/10 border-orange-500/20';\n    }\n    return 'text-blue-400 bg-blue-500/10 border-blue-500/20';\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"achievements\",\n    className: \"py-20 bg-dark-900\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl sm:text-5xl font-bold gradient-text mb-4\",\n          children: \"Achievements & Awards\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-dark-300 max-w-3xl mx-auto\",\n          children: \"Recognition for innovation, leadership, and technical excellence in competitions and organizations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"flex flex-wrap justify-center gap-3 mb-12\",\n        children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(motion.button, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          whileInView: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.4,\n            delay: 0.1 * index\n          },\n          onClick: () => setActiveFilter(category.name),\n          className: `flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${activeFilter === category.name ? 'bg-primary-600 text-white shadow-lg scale-105' : 'bg-dark-700 text-dark-300 hover:bg-dark-600 hover:text-white'}`,\n          children: [/*#__PURE__*/_jsxDEV(category.icon, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: category.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this)]\n        }, category.name, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        layout: true,\n        className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\",\n        children: filteredAchievements.map((achievement, index) => {\n          const CategoryIcon = getCategoryIcon(achievement.category);\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            layout: true,\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 0.9\n            },\n            transition: {\n              duration: 0.5,\n              delay: 0.1 * index\n            },\n            className: \"bg-dark-800/50 rounded-lg border border-dark-700 hover:border-primary-500/50 transition-all duration-300 card-hover group overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-3 rounded-lg ${getCategoryColor(achievement.category)}`,\n                  children: /*#__PURE__*/_jsxDEV(CategoryIcon, {\n                    size: 24\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 21\n                }, this), achievement.position && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-3 py-1 rounded-full text-xs font-bold border ${getPositionColor(achievement.position)}`,\n                  children: achievement.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-bold text-white mb-2 group-hover:text-primary-400 transition-colors duration-300\",\n                children: achievement.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-dark-300 text-sm leading-relaxed mb-4\",\n                children: achievement.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 text-sm text-dark-400\",\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: achievement.date\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this), achievement.organization && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 text-sm text-dark-400\",\n                  children: [/*#__PURE__*/_jsxDEV(Building, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: achievement.organization\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 pt-4 border-t border-dark-600\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 rounded text-xs font-medium border ${getCategoryColor(achievement.category)}`,\n                  children: achievement.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)\n          }, achievement.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.6\n        },\n        className: \"grid md:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold gradient-text mb-2\",\n            children: achievements.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-dark-300 text-sm\",\n            children: \"Total Achievements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold gradient-text mb-2\",\n            children: achievements.filter(a => a.category === 'Hackathon').length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-dark-300 text-sm\",\n            children: \"Hackathon Wins\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold gradient-text mb-2\",\n            children: achievements.filter(a => a.category === 'Recognition').length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-dark-300 text-sm\",\n            children: \"Leadership Roles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold gradient-text mb-2\",\n            children: achievements.filter(a => a.position && (a.position.includes('Top') || a.position.includes('Winner'))).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-dark-300 text-sm\",\n            children: \"Top Positions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.8\n        },\n        className: \"mt-16 bg-gradient-to-r from-primary-500/10 via-secondary-500/10 to-accent-500/10 rounded-lg p-8 border border-primary-500/20\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold gradient-text text-center mb-6\",\n          children: \"Notable Highlights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-2\",\n              children: \"\\uD83C\\uDFC6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-white mb-2\",\n              children: \"Smart India Hackathon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-dark-300 text-sm\",\n              children: \"Consistent performer with Top 10 (2022) and Consolation Prize (2024) in India's largest hackathon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-2\",\n              children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-white mb-2\",\n              children: \"Student Leadership\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-dark-300 text-sm\",\n              children: \"Multiple leadership roles including Student Coordinator, Discipline Committee Head, and Welfare Member\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(Achievements, \"ozHZ0P52e9OmiFFgr1T32bValso=\");\n_c = Achievements;\nexport default Achievements;\nvar _c;\n$RefreshReg$(_c, \"Achievements\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Trophy", "Medal", "Award", "Users", "Calendar", "Building", "achievements", "jsxDEV", "_jsxDEV", "Achievements", "_s", "activeFilter", "setActiveFilter", "categories", "name", "icon", "filteredAchievements", "filter", "achievement", "category", "getCategoryIcon", "getCategoryColor", "getPositionColor", "position", "includes", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "map", "index", "button", "scale", "onClick", "size", "layout", "CategoryIcon", "exit", "title", "description", "date", "organization", "length", "a", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/sections/Achievements.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Trophy, Medal, Award, Users, Calendar, Building } from 'lucide-react';\nimport { achievements } from '../../data/portfolio';\n\nconst Achievements: React.FC = () => {\n  const [activeFilter, setActiveFilter] = useState<string>('All');\n\n  const categories = [\n    { name: 'All', icon: Trophy },\n    { name: 'Hackathon', icon: Trophy },\n    { name: 'Competition', icon: Medal },\n    { name: 'Award', icon: Award },\n    { name: 'Recognition', icon: Users },\n  ];\n\n  const filteredAchievements = activeFilter === 'All' \n    ? achievements \n    : achievements.filter(achievement => achievement.category === activeFilter);\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'Hackathon': return Trophy;\n      case 'Competition': return Medal;\n      case 'Award': return Award;\n      case 'Recognition': return Users;\n      default: return Trophy;\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'Hackathon': return 'text-accent-400 bg-accent-500/10 border-accent-500/20';\n      case 'Competition': return 'text-primary-400 bg-primary-500/10 border-primary-500/20';\n      case 'Award': return 'text-secondary-400 bg-secondary-500/10 border-secondary-500/20';\n      case 'Recognition': return 'text-purple-400 bg-purple-500/10 border-purple-500/20';\n      default: return 'text-dark-400 bg-dark-500/10 border-dark-500/20';\n    }\n  };\n\n  const getPositionColor = (position?: string) => {\n    if (!position) return '';\n    if (position.includes('1st') || position.includes('Winner') || position.includes('Top 10')) {\n      return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';\n    }\n    if (position.includes('2nd') || position.includes('Runner')) {\n      return 'text-gray-300 bg-gray-500/10 border-gray-500/20';\n    }\n    if (position.includes('3rd') || position.includes('Consolation')) {\n      return 'text-orange-400 bg-orange-500/10 border-orange-500/20';\n    }\n    return 'text-blue-400 bg-blue-500/10 border-blue-500/20';\n  };\n\n  return (\n    <section id=\"achievements\" className=\"py-20 bg-dark-900\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-4\">\n            Achievements & Awards\n          </h2>\n          <p className=\"text-xl text-dark-300 max-w-3xl mx-auto\">\n            Recognition for innovation, leadership, and technical excellence in competitions and organizations\n          </p>\n        </motion.div>\n\n        {/* Category Filter */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex flex-wrap justify-center gap-3 mb-12\"\n        >\n          {categories.map((category, index) => (\n            <motion.button\n              key={category.name}\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.4, delay: 0.1 * index }}\n              onClick={() => setActiveFilter(category.name)}\n              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${\n                activeFilter === category.name\n                  ? 'bg-primary-600 text-white shadow-lg scale-105'\n                  : 'bg-dark-700 text-dark-300 hover:bg-dark-600 hover:text-white'\n              }`}\n            >\n              <category.icon size={16} />\n              <span className=\"text-sm\">{category.name}</span>\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* Achievements Grid */}\n        <motion.div\n          layout\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\"\n        >\n          {filteredAchievements.map((achievement, index) => {\n            const CategoryIcon = getCategoryIcon(achievement.category);\n            \n            return (\n              <motion.div\n                key={achievement.id}\n                layout\n                initial={{ opacity: 0, scale: 0.9 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                exit={{ opacity: 0, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: 0.1 * index }}\n                className=\"bg-dark-800/50 rounded-lg border border-dark-700 hover:border-primary-500/50 transition-all duration-300 card-hover group overflow-hidden\"\n              >\n                <div className=\"p-6\">\n                  {/* Header */}\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className={`p-3 rounded-lg ${getCategoryColor(achievement.category)}`}>\n                      <CategoryIcon size={24} />\n                    </div>\n                    \n                    {achievement.position && (\n                      <div className={`px-3 py-1 rounded-full text-xs font-bold border ${getPositionColor(achievement.position)}`}>\n                        {achievement.position}\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Title */}\n                  <h3 className=\"text-lg font-bold text-white mb-2 group-hover:text-primary-400 transition-colors duration-300\">\n                    {achievement.title}\n                  </h3>\n\n                  {/* Description */}\n                  <p className=\"text-dark-300 text-sm leading-relaxed mb-4\">\n                    {achievement.description}\n                  </p>\n\n                  {/* Details */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center gap-2 text-sm text-dark-400\">\n                      <Calendar size={14} />\n                      <span>{achievement.date}</span>\n                    </div>\n                    \n                    {achievement.organization && (\n                      <div className=\"flex items-center gap-2 text-sm text-dark-400\">\n                        <Building size={14} />\n                        <span>{achievement.organization}</span>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Category Badge */}\n                  <div className=\"mt-4 pt-4 border-t border-dark-600\">\n                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getCategoryColor(achievement.category)}`}>\n                      {achievement.category}\n                    </span>\n                  </div>\n                </div>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        {/* Achievements Summary */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"grid md:grid-cols-4 gap-6\"\n        >\n          <div className=\"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">\n              {achievements.length}\n            </div>\n            <div className=\"text-dark-300 text-sm\">Total Achievements</div>\n          </div>\n          \n          <div className=\"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">\n              {achievements.filter(a => a.category === 'Hackathon').length}\n            </div>\n            <div className=\"text-dark-300 text-sm\">Hackathon Wins</div>\n          </div>\n          \n          <div className=\"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">\n              {achievements.filter(a => a.category === 'Recognition').length}\n            </div>\n            <div className=\"text-dark-300 text-sm\">Leadership Roles</div>\n          </div>\n          \n          <div className=\"text-center p-6 bg-dark-800/30 rounded-lg border border-dark-700\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">\n              {achievements.filter(a => a.position && (a.position.includes('Top') || a.position.includes('Winner'))).length}\n            </div>\n            <div className=\"text-dark-300 text-sm\">Top Positions</div>\n          </div>\n        </motion.div>\n\n        {/* Notable Highlights */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          className=\"mt-16 bg-gradient-to-r from-primary-500/10 via-secondary-500/10 to-accent-500/10 rounded-lg p-8 border border-primary-500/20\"\n        >\n          <h3 className=\"text-2xl font-bold gradient-text text-center mb-6\">\n            Notable Highlights\n          </h3>\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">🏆</div>\n              <h4 className=\"text-lg font-semibold text-white mb-2\">Smart India Hackathon</h4>\n              <p className=\"text-dark-300 text-sm\">\n                Consistent performer with Top 10 (2022) and Consolation Prize (2024) in India's largest hackathon\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">👨‍💼</div>\n              <h4 className=\"text-lg font-semibold text-white mb-2\">Student Leadership</h4>\n              <p className=\"text-dark-300 text-sm\">\n                Multiple leadership roles including Student Coordinator, Discipline Committee Head, and Welfare Member\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Achievements;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AAC9E,SAASC,YAAY,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAS,KAAK,CAAC;EAE/D,MAAMe,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAEf;EAAO,CAAC,EAC7B;IAAEc,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEf;EAAO,CAAC,EACnC;IAAEc,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAEd;EAAM,CAAC,EACpC;IAAEa,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAEb;EAAM,CAAC,EAC9B;IAAEY,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAEZ;EAAM,CAAC,CACrC;EAED,MAAMa,oBAAoB,GAAGL,YAAY,KAAK,KAAK,GAC/CL,YAAY,GACZA,YAAY,CAACW,MAAM,CAACC,WAAW,IAAIA,WAAW,CAACC,QAAQ,KAAKR,YAAY,CAAC;EAE7E,MAAMS,eAAe,GAAID,QAAgB,IAAK;IAC5C,QAAQA,QAAQ;MACd,KAAK,WAAW;QAAE,OAAOnB,MAAM;MAC/B,KAAK,aAAa;QAAE,OAAOC,KAAK;MAChC,KAAK,OAAO;QAAE,OAAOC,KAAK;MAC1B,KAAK,aAAa;QAAE,OAAOC,KAAK;MAChC;QAAS,OAAOH,MAAM;IACxB;EACF,CAAC;EAED,MAAMqB,gBAAgB,GAAIF,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,WAAW;QAAE,OAAO,uDAAuD;MAChF,KAAK,aAAa;QAAE,OAAO,0DAA0D;MACrF,KAAK,OAAO;QAAE,OAAO,gEAAgE;MACrF,KAAK,aAAa;QAAE,OAAO,uDAAuD;MAClF;QAAS,OAAO,iDAAiD;IACnE;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAIC,QAAiB,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IACxB,IAAIA,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1F,OAAO,uDAAuD;IAChE;IACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3D,OAAO,iDAAiD;IAC1D;IACA,IAAID,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,QAAQ,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;MAChE,OAAO,uDAAuD;IAChE;IACA,OAAO,iDAAiD;EAC1D,CAAC;EAED,oBACEhB,OAAA;IAASiB,EAAE,EAAC,cAAc;IAACC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eACtDnB,OAAA;MAAKkB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBAErDnB,OAAA,CAACT,MAAM,CAAC6B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BnB,OAAA;UAAIkB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9B,OAAA;UAAGkB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb9B,OAAA,CAACT,MAAM,CAAC6B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAEpDd,UAAU,CAAC2B,GAAG,CAAC,CAACrB,QAAQ,EAAEsB,KAAK,kBAC9BjC,OAAA,CAACT,MAAM,CAAC2C,MAAM;UAEZb,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEa,KAAK,EAAE;UAAI,CAAE;UACpCX,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEa,KAAK,EAAE;UAAE,CAAE;UACtCV,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEK,KAAK,EAAE,GAAG,GAAGE;UAAM,CAAE;UAClDG,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAACO,QAAQ,CAACL,IAAI,CAAE;UAC9CY,SAAS,EAAE,wFACTf,YAAY,KAAKQ,QAAQ,CAACL,IAAI,GAC1B,+CAA+C,GAC/C,8DAA8D,EACjE;UAAAa,QAAA,gBAEHnB,OAAA,CAACW,QAAQ,CAACJ,IAAI;YAAC8B,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3B9B,OAAA;YAAMkB,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAER,QAAQ,CAACL;UAAI;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAZ3CnB,QAAQ,CAACL,IAAI;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaL,CAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGb9B,OAAA,CAACT,MAAM,CAAC6B,GAAG;QACTkB,MAAM;QACNpB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAEzDX,oBAAoB,CAACwB,GAAG,CAAC,CAACtB,WAAW,EAAEuB,KAAK,KAAK;UAChD,MAAMM,YAAY,GAAG3B,eAAe,CAACF,WAAW,CAACC,QAAQ,CAAC;UAE1D,oBACEX,OAAA,CAACT,MAAM,CAAC6B,GAAG;YAETkB,MAAM;YACNjB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEa,KAAK,EAAE;YAAI,CAAE;YACpCX,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEa,KAAK,EAAE;YAAE,CAAE;YACtCK,IAAI,EAAE;cAAElB,OAAO,EAAE,CAAC;cAAEa,KAAK,EAAE;YAAI,CAAE;YACjCV,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEK,KAAK,EAAE,GAAG,GAAGE;YAAM,CAAE;YAClDf,SAAS,EAAC,2IAA2I;YAAAC,QAAA,eAErJnB,OAAA;cAAKkB,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAElBnB,OAAA;gBAAKkB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDnB,OAAA;kBAAKkB,SAAS,EAAE,kBAAkBL,gBAAgB,CAACH,WAAW,CAACC,QAAQ,CAAC,EAAG;kBAAAQ,QAAA,eACzEnB,OAAA,CAACuC,YAAY;oBAACF,IAAI,EAAE;kBAAG;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,EAELpB,WAAW,CAACK,QAAQ,iBACnBf,OAAA;kBAAKkB,SAAS,EAAE,mDAAmDJ,gBAAgB,CAACJ,WAAW,CAACK,QAAQ,CAAC,EAAG;kBAAAI,QAAA,EACzGT,WAAW,CAACK;gBAAQ;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGN9B,OAAA;gBAAIkB,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,EAC1GT,WAAW,CAAC+B;cAAK;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eAGL9B,OAAA;gBAAGkB,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACtDT,WAAW,CAACgC;cAAW;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAGJ9B,OAAA;gBAAKkB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnB,OAAA;kBAAKkB,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DnB,OAAA,CAACJ,QAAQ;oBAACyC,IAAI,EAAE;kBAAG;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtB9B,OAAA;oBAAAmB,QAAA,EAAOT,WAAW,CAACiC;kBAAI;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,EAELpB,WAAW,CAACkC,YAAY,iBACvB5C,OAAA;kBAAKkB,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DnB,OAAA,CAACH,QAAQ;oBAACwC,IAAI,EAAE;kBAAG;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtB9B,OAAA;oBAAAmB,QAAA,EAAOT,WAAW,CAACkC;kBAAY;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGN9B,OAAA;gBAAKkB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDnB,OAAA;kBAAMkB,SAAS,EAAE,gDAAgDL,gBAAgB,CAACH,WAAW,CAACC,QAAQ,CAAC,EAAG;kBAAAQ,QAAA,EACvGT,WAAW,CAACC;gBAAQ;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GArDDpB,WAAW,CAACO,EAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsDT,CAAC;QAEjB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGb9B,OAAA,CAACT,MAAM,CAAC6B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAErCnB,OAAA;UAAKkB,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EnB,OAAA;YAAKkB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDrB,YAAY,CAAC+C;UAAM;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACN9B,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAkB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAEN9B,OAAA;UAAKkB,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EnB,OAAA;YAAKkB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDrB,YAAY,CAACW,MAAM,CAACqC,CAAC,IAAIA,CAAC,CAACnC,QAAQ,KAAK,WAAW,CAAC,CAACkC;UAAM;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN9B,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAEN9B,OAAA;UAAKkB,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EnB,OAAA;YAAKkB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDrB,YAAY,CAACW,MAAM,CAACqC,CAAC,IAAIA,CAAC,CAACnC,QAAQ,KAAK,aAAa,CAAC,CAACkC;UAAM;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACN9B,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAgB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEN9B,OAAA;UAAKkB,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EnB,OAAA;YAAKkB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDrB,YAAY,CAACW,MAAM,CAACqC,CAAC,IAAIA,CAAC,CAAC/B,QAAQ,KAAK+B,CAAC,CAAC/B,QAAQ,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAI8B,CAAC,CAAC/B,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC6B;UAAM;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eACN9B,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb9B,OAAA,CAACT,MAAM,CAAC6B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,8HAA8H;QAAAC,QAAA,gBAExInB,OAAA;UAAIkB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9B,OAAA;UAAKkB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCnB,OAAA;YAAKkB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnB,OAAA;cAAKkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvC9B,OAAA;cAAIkB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChF9B,OAAA;cAAGkB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9B,OAAA;YAAKkB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnB,OAAA;cAAKkB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1C9B,OAAA;cAAIkB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7E9B,OAAA;cAAGkB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC5B,EAAA,CApOID,YAAsB;AAAA8C,EAAA,GAAtB9C,YAAsB;AAsO5B,eAAeA,YAAY;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}