{"ast": null, "code": "import { resolveElements } from '../../utils/resolve-elements.mjs';\nfunction createSelectorEffect(subjectEffect) {\n  return (subject, values) => {\n    const elements = resolveElements(subject);\n    const subscriptions = [];\n    for (const element of elements) {\n      const remove = subjectEffect(element, values);\n      subscriptions.push(remove);\n    }\n    return () => {\n      for (const remove of subscriptions) remove();\n    };\n  };\n}\nexport { createSelectorEffect };", "map": {"version": 3, "names": ["resolveElements", "createSelectorEffect", "subjectEffect", "subject", "values", "elements", "subscriptions", "element", "remove", "push"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/effects/utils/create-dom-effect.mjs"], "sourcesContent": ["import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction createSelectorEffect(subjectEffect) {\n    return (subject, values) => {\n        const elements = resolveElements(subject);\n        const subscriptions = [];\n        for (const element of elements) {\n            const remove = subjectEffect(element, values);\n            subscriptions.push(remove);\n        }\n        return () => {\n            for (const remove of subscriptions)\n                remove();\n        };\n    };\n}\n\nexport { createSelectorEffect };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kCAAkC;AAElE,SAASC,oBAAoBA,CAACC,aAAa,EAAE;EACzC,OAAO,CAACC,OAAO,EAAEC,MAAM,KAAK;IACxB,MAAMC,QAAQ,GAAGL,eAAe,CAACG,OAAO,CAAC;IACzC,MAAMG,aAAa,GAAG,EAAE;IACxB,KAAK,MAAMC,OAAO,IAAIF,QAAQ,EAAE;MAC5B,MAAMG,MAAM,GAAGN,aAAa,CAACK,OAAO,EAAEH,MAAM,CAAC;MAC7CE,aAAa,CAACG,IAAI,CAACD,MAAM,CAAC;IAC9B;IACA,OAAO,MAAM;MACT,KAAK,MAAMA,MAAM,IAAIF,aAAa,EAC9BE,MAAM,CAAC,CAAC;IAChB,CAAC;EACL,CAAC;AACL;AAEA,SAASP,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}