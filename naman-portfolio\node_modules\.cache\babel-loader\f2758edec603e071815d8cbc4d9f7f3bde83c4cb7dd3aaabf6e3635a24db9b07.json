{"ast": null, "code": "import { warnOnce } from 'motion-utils';\nimport { createMotionComponent } from '../../motion/index.mjs';\nfunction createMotionProxy(preloadedFeatures, createVisualElement) {\n  if (typeof Proxy === \"undefined\") {\n    return createMotionComponent;\n  }\n  /**\n   * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n   * Rather than generating them anew every render.\n   */\n  const componentCache = new Map();\n  const factory = (Component, options) => {\n    return createMotionComponent(Component, options, preloadedFeatures, createVisualElement);\n  };\n  /**\n   * Support for deprecated`motion(Component)` pattern\n   */\n  const deprecatedFactoryFunction = (Component, options) => {\n    if (process.env.NODE_ENV !== \"production\") {\n      warnOnce(false, \"motion() is deprecated. Use motion.create() instead.\");\n    }\n    return factory(Component, options);\n  };\n  return new Proxy(deprecatedFactoryFunction, {\n    /**\n     * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n     * The prop name is passed through as `key` and we can use that to generate a `motion`\n     * DOM component with that name.\n     */\n    get: (_target, key) => {\n      if (key === \"create\") return factory;\n      /**\n       * If this element doesn't exist in the component cache, create it and cache.\n       */\n      if (!componentCache.has(key)) {\n        componentCache.set(key, createMotionComponent(key, undefined, preloadedFeatures, createVisualElement));\n      }\n      return componentCache.get(key);\n    }\n  });\n}\nexport { createMotionProxy };", "map": {"version": 3, "names": ["warnOnce", "createMotionComponent", "createMotionProxy", "preloadedFeatures", "createVisualElement", "Proxy", "componentCache", "Map", "factory", "Component", "options", "deprecatedFactoryFunction", "process", "env", "NODE_ENV", "get", "_target", "key", "has", "set", "undefined"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs"], "sourcesContent": ["import { warnOnce } from 'motion-utils';\nimport { createMotionComponent } from '../../motion/index.mjs';\n\nfunction createMotionProxy(preloadedFeatures, createVisualElement) {\n    if (typeof Proxy === \"undefined\") {\n        return createMotionComponent;\n    }\n    /**\n     * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n     * Rather than generating them anew every render.\n     */\n    const componentCache = new Map();\n    const factory = (Component, options) => {\n        return createMotionComponent(Component, options, preloadedFeatures, createVisualElement);\n    };\n    /**\n     * Support for deprecated`motion(Component)` pattern\n     */\n    const deprecatedFactoryFunction = (Component, options) => {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, \"motion() is deprecated. Use motion.create() instead.\");\n        }\n        return factory(Component, options);\n    };\n    return new Proxy(deprecatedFactoryFunction, {\n        /**\n         * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n         * The prop name is passed through as `key` and we can use that to generate a `motion`\n         * DOM component with that name.\n         */\n        get: (_target, key) => {\n            if (key === \"create\")\n                return factory;\n            /**\n             * If this element doesn't exist in the component cache, create it and cache.\n             */\n            if (!componentCache.has(key)) {\n                componentCache.set(key, createMotionComponent(key, undefined, preloadedFeatures, createVisualElement));\n            }\n            return componentCache.get(key);\n        },\n    });\n}\n\nexport { createMotionProxy };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,qBAAqB,QAAQ,wBAAwB;AAE9D,SAASC,iBAAiBA,CAACC,iBAAiB,EAAEC,mBAAmB,EAAE;EAC/D,IAAI,OAAOC,KAAK,KAAK,WAAW,EAAE;IAC9B,OAAOJ,qBAAqB;EAChC;EACA;AACJ;AACA;AACA;EACI,MAAMK,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC,MAAMC,OAAO,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;IACpC,OAAOT,qBAAqB,CAACQ,SAAS,EAAEC,OAAO,EAAEP,iBAAiB,EAAEC,mBAAmB,CAAC;EAC5F,CAAC;EACD;AACJ;AACA;EACI,MAAMO,yBAAyB,GAAGA,CAACF,SAAS,EAAEC,OAAO,KAAK;IACtD,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvCd,QAAQ,CAAC,KAAK,EAAE,sDAAsD,CAAC;IAC3E;IACA,OAAOQ,OAAO,CAACC,SAAS,EAAEC,OAAO,CAAC;EACtC,CAAC;EACD,OAAO,IAAIL,KAAK,CAACM,yBAAyB,EAAE;IACxC;AACR;AACA;AACA;AACA;IACQI,GAAG,EAAEA,CAACC,OAAO,EAAEC,GAAG,KAAK;MACnB,IAAIA,GAAG,KAAK,QAAQ,EAChB,OAAOT,OAAO;MAClB;AACZ;AACA;MACY,IAAI,CAACF,cAAc,CAACY,GAAG,CAACD,GAAG,CAAC,EAAE;QAC1BX,cAAc,CAACa,GAAG,CAACF,GAAG,EAAEhB,qBAAqB,CAACgB,GAAG,EAAEG,SAAS,EAAEjB,iBAAiB,EAAEC,mBAAmB,CAAC,CAAC;MAC1G;MACA,OAAOE,cAAc,CAACS,GAAG,CAACE,GAAG,CAAC;IAClC;EACJ,CAAC,CAAC;AACN;AAEA,SAASf,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}