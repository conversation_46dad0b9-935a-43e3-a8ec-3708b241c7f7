{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Download, Github, Linkedin, Mail, ExternalLink, Terminal, Code, Cpu, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  _s();\n  const [typedText, setTypedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const roles = ['AI/ML Engineer', 'Full-Stack Developer', 'Automation Expert', 'Innovation Catalyst'];\n  const socialLinks = [{\n    icon: Github,\n    href: 'https://github.com/naman2002',\n    label: 'GitHub',\n    color: '#00ffff'\n  }, {\n    icon: Linkedin,\n    href: 'https://linkedin.com/in/naman-nagi-92026521',\n    label: 'LinkedIn',\n    color: '#0080ff'\n  }, {\n    icon: ExternalLink,\n    href: 'https://technaman.tech',\n    label: 'Website',\n    color: '#8000ff'\n  }, {\n    icon: Mail,\n    href: 'mailto:<EMAIL>',\n    label: 'Email',\n    color: '#ff0080'\n  }];\n  const techIcons = [{\n    icon: Terminal,\n    label: 'Terminal'\n  }, {\n    icon: Code,\n    label: 'Code'\n  }, {\n    icon: Cpu,\n    label: 'AI/ML'\n  }, {\n    icon: Zap,\n    label: 'Automation'\n  }];\n\n  // Typewriter effect\n  useEffect(() => {\n    const currentRole = roles[currentIndex];\n    let charIndex = 0;\n    const typeInterval = setInterval(() => {\n      if (charIndex < currentRole.length) {\n        setTypedText(currentRole.slice(0, charIndex + 1));\n        charIndex++;\n      } else {\n        clearInterval(typeInterval);\n        setTimeout(() => {\n          const deleteInterval = setInterval(() => {\n            if (charIndex > 0) {\n              setTypedText(currentRole.slice(0, charIndex - 1));\n              charIndex--;\n            } else {\n              clearInterval(deleteInterval);\n              setCurrentIndex(prev => (prev + 1) % roles.length);\n            }\n          }, 50);\n        }, 2000);\n      }\n    }, 100);\n    return () => clearInterval(typeInterval);\n  }, [currentIndex]);\n  const scrollToAbout = () => {\n    const aboutSection = document.querySelector('#about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"min-h-screen flex items-center justify-center relative overflow-hidden bg-black\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        style: {\n          backgroundImage: `\n            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px',\n          animation: 'grid-move 20s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [techIcons.map((tech, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute text-cyan-400/30\",\n        initial: {\n          opacity: 0,\n          scale: 0\n        },\n        animate: {\n          opacity: [0.3, 0.7, 0.3],\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 8,\n          repeat: Infinity,\n          delay: index * 2,\n          ease: \"easeInOut\"\n        },\n        style: {\n          top: `${20 + index * 20}%`,\n          left: `${10 + index * 20}%`\n        },\n        children: /*#__PURE__*/_jsxDEV(tech.icon, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this)\n      }, tech.label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this)), [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-px bg-gradient-to-b from-transparent via-cyan-400 to-transparent\",\n        style: {\n          left: `${15 + i * 15}%`,\n          height: '200px'\n        },\n        animate: {\n          y: ['-200px', '100vh'],\n          opacity: [0, 1, 0]\n        },\n        transition: {\n          duration: 3,\n          repeat: Infinity,\n          delay: i * 0.5,\n          ease: \"linear\"\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block bg-black/80 border border-cyan-400/50 rounded-lg p-4 font-mono text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 bg-red-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 bg-yellow-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3 h-3 bg-green-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-4 text-cyan-400 text-sm\",\n                children: \"terminal@naman-nagi:~$\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: \"auto\"\n              },\n              transition: {\n                delay: 0.5,\n                duration: 1\n              },\n              className: \"text-green-400 text-sm overflow-hidden whitespace-nowrap\",\n              children: \"./initialize_portfolio.sh --mode=professional\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.3\n          },\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.6,\n              duration: 1,\n              type: \"spring\"\n            },\n            className: \"text-6xl sm:text-7xl lg:text-9xl font-black gradient-text leading-tight mb-6 glitch\",\n            \"data-text\": \"NAMAN NAGI\",\n            children: \"NAMAN NAGI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.8,\n              duration: 0.8\n            },\n            className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-cyan-400\",\n              children: \">\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"terminal-text\",\n              children: typedText\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-pulse text-cyan-400\",\n              children: \"|\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"neon-card p-8 rounded-2xl scan-lines\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl sm:text-2xl text-gray-300 leading-relaxed\",\n                children: [\"Building \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-cyan-400 font-semibold glow-text\",\n                  children: \"energy-efficient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 28\n                }, this), \",\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-purple-400 font-semibold glow-text\",\n                  children: \" automated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), \", and\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-pink-400 font-semibold glow-text\",\n                  children: \" scalable AI solutions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), \" with\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-yellow-400 font-bold\",\n                  children: \"2+ years\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this), \" of experience.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-400 mt-4\",\n                children: \"Passionate about sustainable impact in agriculture, environment monitoring, and smart infrastructure.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#contact\",\n              onClick: e => {\n                e.preventDefault();\n                const contactSection = document.querySelector('#contact');\n                if (contactSection) {\n                  contactSection.scrollIntoView({\n                    behavior: 'smooth'\n                  });\n                }\n              },\n              className: \"btn-primary inline-flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), \"Get In Touch\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/resume.pdf\",\n              download: true,\n              className: \"btn-secondary inline-flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), \"Download Resume\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              delay: 1.2,\n              duration: 0.8\n            },\n            className: \"flex justify-center space-x-6 mt-8\",\n            children: socialLinks.map((link, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              initial: {\n                opacity: 0,\n                scale: 0\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.4 + 0.1 * index,\n                duration: 0.4\n              },\n              href: link.href,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"text-dark-400 hover:text-primary-400 transition-all duration-300 hover:scale-110 transform p-3 rounded-full hover:bg-dark-800/50\",\n              \"aria-label\": link.label,\n              children: /*#__PURE__*/_jsxDEV(link.icon, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)\n            }, link.label, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            delay: 1.6,\n            duration: 0.8\n          },\n          className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: scrollToAbout,\n            className: \"text-dark-400 hover:text-primary-400 transition-colors duration-300 animate-bounce\",\n            \"aria-label\": \"Scroll to about section\",\n            children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n              size: 32\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      animate: {\n        y: [0, -20, 0],\n        rotate: [0, 5, 0]\n      },\n      transition: {\n        duration: 6,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      },\n      className: \"absolute top-20 left-10 w-20 h-20 bg-primary-500/10 rounded-lg blur-sm\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      animate: {\n        y: [0, 20, 0],\n        rotate: [0, -5, 0]\n      },\n      transition: {\n        duration: 8,\n        repeat: Infinity,\n        ease: \"easeInOut\",\n        delay: 1\n      },\n      className: \"absolute bottom-20 right-10 w-16 h-16 bg-secondary-500/10 rounded-full blur-sm\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(Hero, \"0+ib4fLRCPqQo7iZy97ndy1TPeQ=\");\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "ChevronDown", "Download", "<PERSON><PERSON><PERSON>", "Linkedin", "Mail", "ExternalLink", "Terminal", "Code", "Cpu", "Zap", "jsxDEV", "_jsxDEV", "Hero", "_s", "typedText", "setTypedText", "currentIndex", "setCurrentIndex", "roles", "socialLinks", "icon", "href", "label", "color", "techIcons", "currentRole", "charIndex", "typeInterval", "setInterval", "length", "slice", "clearInterval", "setTimeout", "deleteInterval", "prev", "scrollToAbout", "aboutSection", "document", "querySelector", "scrollIntoView", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundImage", "backgroundSize", "animation", "map", "tech", "index", "div", "initial", "opacity", "scale", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "delay", "ease", "top", "left", "size", "Array", "_", "i", "height", "y", "p", "width", "h1", "type", "onClick", "e", "preventDefault", "contactSection", "download", "link", "a", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/sections/Hero.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Download, Github, Linkedin, Mail, ExternalLink, Terminal, Code, Cpu, Zap } from 'lucide-react';\n\nconst Hero: React.FC = () => {\n  const [typedText, setTypedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  const roles = [\n    'AI/ML Engineer',\n    'Full-Stack Developer',\n    'Automation Expert',\n    'Innovation Catalyst'\n  ];\n\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com/naman2002', label: 'GitHub', color: '#00ffff' },\n    { icon: Linkedin, href: 'https://linkedin.com/in/naman-nagi-92026521', label: 'LinkedIn', color: '#0080ff' },\n    { icon: ExternalLink, href: 'https://technaman.tech', label: 'Website', color: '#8000ff' },\n    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email', color: '#ff0080' },\n  ];\n\n  const techIcons = [\n    { icon: Terminal, label: 'Terminal' },\n    { icon: Code, label: 'Code' },\n    { icon: Cpu, label: 'AI/ML' },\n    { icon: Zap, label: 'Automation' },\n  ];\n\n  // Typewriter effect\n  useEffect(() => {\n    const currentRole = roles[currentIndex];\n    let charIndex = 0;\n\n    const typeInterval = setInterval(() => {\n      if (charIndex < currentRole.length) {\n        setTypedText(currentRole.slice(0, charIndex + 1));\n        charIndex++;\n      } else {\n        clearInterval(typeInterval);\n        setTimeout(() => {\n          const deleteInterval = setInterval(() => {\n            if (charIndex > 0) {\n              setTypedText(currentRole.slice(0, charIndex - 1));\n              charIndex--;\n            } else {\n              clearInterval(deleteInterval);\n              setCurrentIndex((prev) => (prev + 1) % roles.length);\n            }\n          }, 50);\n        }, 2000);\n      }\n    }, 100);\n\n    return () => clearInterval(typeInterval);\n  }, [currentIndex]);\n\n  const scrollToAbout = () => {\n    const aboutSection = document.querySelector('#about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-black\">\n      {/* Cyber Grid Background */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10\"></div>\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `\n            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px',\n          animation: 'grid-move 20s linear infinite'\n        }}></div>\n      </div>\n\n      {/* Floating Tech Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {techIcons.map((tech, index) => (\n          <motion.div\n            key={tech.label}\n            className=\"absolute text-cyan-400/30\"\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{\n              opacity: [0.3, 0.7, 0.3],\n              scale: [1, 1.2, 1],\n              rotate: [0, 180, 360]\n            }}\n            transition={{\n              duration: 8,\n              repeat: Infinity,\n              delay: index * 2,\n              ease: \"easeInOut\"\n            }}\n            style={{\n              top: `${20 + index * 20}%`,\n              left: `${10 + index * 20}%`,\n            }}\n          >\n            <tech.icon size={40} />\n          </motion.div>\n        ))}\n\n        {/* Data streams */}\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-px bg-gradient-to-b from-transparent via-cyan-400 to-transparent\"\n            style={{\n              left: `${15 + i * 15}%`,\n              height: '200px',\n            }}\n            animate={{\n              y: ['-200px', '100vh'],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: 3,\n              repeat: Infinity,\n              delay: i * 0.5,\n              ease: \"linear\"\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center\">\n          {/* Terminal-style greeting */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"mb-8\"\n          >\n            <div className=\"inline-block bg-black/80 border border-cyan-400/50 rounded-lg p-4 font-mono text-left\">\n              <div className=\"flex items-center mb-2\">\n                <div className=\"flex space-x-2\">\n                  <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n                  <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                </div>\n                <span className=\"ml-4 text-cyan-400 text-sm\">terminal@naman-nagi:~$</span>\n              </div>\n              <motion.p\n                initial={{ width: 0 }}\n                animate={{ width: \"auto\" }}\n                transition={{ delay: 0.5, duration: 1 }}\n                className=\"text-green-400 text-sm overflow-hidden whitespace-nowrap\"\n              >\n                ./initialize_portfolio.sh --mode=professional\n              </motion.p>\n            </div>\n          </motion.div>\n\n          {/* Main Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            className=\"space-y-8\"\n          >\n            {/* Name with glitch effect */}\n            <motion.h1\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.6, duration: 1, type: \"spring\" }}\n              className=\"text-6xl sm:text-7xl lg:text-9xl font-black gradient-text leading-tight mb-6 glitch\"\n              data-text=\"NAMAN NAGI\"\n            >\n              NAMAN NAGI\n            </motion.h1>\n\n            {/* Animated role title */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.8 }}\n              className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8\"\n            >\n              <span className=\"text-cyan-400\">&gt;</span>{' '}\n              <span className=\"terminal-text\">{typedText}</span>\n              <span className=\"animate-pulse text-cyan-400\">|</span>\n            </motion.div>\n\n            {/* Description with cyber styling */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.8 }}\n              className=\"max-w-4xl mx-auto\"\n            >\n              <div className=\"neon-card p-8 rounded-2xl scan-lines\">\n                <p className=\"text-xl sm:text-2xl text-gray-300 leading-relaxed\">\n                  Building <span className=\"text-cyan-400 font-semibold glow-text\">energy-efficient</span>,\n                  <span className=\"text-purple-400 font-semibold glow-text\"> automated</span>, and\n                  <span className=\"text-pink-400 font-semibold glow-text\"> scalable AI solutions</span> with\n                  <span className=\"text-yellow-400 font-bold\">2+ years</span> of experience.\n                </p>\n                <p className=\"text-lg text-gray-400 mt-4\">\n                  Passionate about sustainable impact in agriculture, environment monitoring, and smart infrastructure.\n                </p>\n              </div>\n            </motion.div>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.8 }}\n              className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mt-12\"\n            >\n              <a\n                href=\"#contact\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  const contactSection = document.querySelector('#contact');\n                  if (contactSection) {\n                    contactSection.scrollIntoView({ behavior: 'smooth' });\n                  }\n                }}\n                className=\"btn-primary inline-flex items-center gap-2\"\n              >\n                <Mail size={20} />\n                Get In Touch\n              </a>\n              \n              <a\n                href=\"/resume.pdf\"\n                download\n                className=\"btn-secondary inline-flex items-center gap-2\"\n              >\n                <Download size={20} />\n                Download Resume\n              </a>\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 1.2, duration: 0.8 }}\n              className=\"flex justify-center space-x-6 mt-8\"\n            >\n              {socialLinks.map((link, index) => (\n                <motion.a\n                  key={link.label}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.4 + 0.1 * index, duration: 0.4 }}\n                  href={link.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-dark-400 hover:text-primary-400 transition-all duration-300 hover:scale-110 transform p-3 rounded-full hover:bg-dark-800/50\"\n                  aria-label={link.label}\n                >\n                  <link.icon size={24} />\n                </motion.a>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Scroll Indicator */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 1.6, duration: 0.8 }}\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          >\n            <button\n              onClick={scrollToAbout}\n              className=\"text-dark-400 hover:text-primary-400 transition-colors duration-300 animate-bounce\"\n              aria-label=\"Scroll to about section\"\n            >\n              <ChevronDown size={32} />\n            </button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Floating Elements */}\n      <motion.div\n        animate={{\n          y: [0, -20, 0],\n          rotate: [0, 5, 0],\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n        className=\"absolute top-20 left-10 w-20 h-20 bg-primary-500/10 rounded-lg blur-sm\"\n      />\n      \n      <motion.div\n        animate={{\n          y: [0, 20, 0],\n          rotate: [0, -5, 0],\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1,\n        }}\n        className=\"absolute bottom-20 right-10 w-16 h-16 bg-secondary-500/10 rounded-full blur-sm\"\n      />\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErH,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAMoB,KAAK,GAAG,CACZ,gBAAgB,EAChB,sBAAsB,EACtB,mBAAmB,EACnB,qBAAqB,CACtB;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAElB,MAAM;IAAEmB,IAAI,EAAE,8BAA8B;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzF;IAAEH,IAAI,EAAEjB,QAAQ;IAAEkB,IAAI,EAAE,6CAA6C;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5G;IAAEH,IAAI,EAAEf,YAAY;IAAEgB,IAAI,EAAE,wBAAwB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC1F;IAAEH,IAAI,EAAEhB,IAAI;IAAEiB,IAAI,EAAE,gCAAgC;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,CACzF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEJ,IAAI,EAAEd,QAAQ;IAAEgB,KAAK,EAAE;EAAW,CAAC,EACrC;IAAEF,IAAI,EAAEb,IAAI;IAAEe,KAAK,EAAE;EAAO,CAAC,EAC7B;IAAEF,IAAI,EAAEZ,GAAG;IAAEc,KAAK,EAAE;EAAQ,CAAC,EAC7B;IAAEF,IAAI,EAAEX,GAAG;IAAEa,KAAK,EAAE;EAAa,CAAC,CACnC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM4B,WAAW,GAAGP,KAAK,CAACF,YAAY,CAAC;IACvC,IAAIU,SAAS,GAAG,CAAC;IAEjB,MAAMC,YAAY,GAAGC,WAAW,CAAC,MAAM;MACrC,IAAIF,SAAS,GAAGD,WAAW,CAACI,MAAM,EAAE;QAClCd,YAAY,CAACU,WAAW,CAACK,KAAK,CAAC,CAAC,EAAEJ,SAAS,GAAG,CAAC,CAAC,CAAC;QACjDA,SAAS,EAAE;MACb,CAAC,MAAM;QACLK,aAAa,CAACJ,YAAY,CAAC;QAC3BK,UAAU,CAAC,MAAM;UACf,MAAMC,cAAc,GAAGL,WAAW,CAAC,MAAM;YACvC,IAAIF,SAAS,GAAG,CAAC,EAAE;cACjBX,YAAY,CAACU,WAAW,CAACK,KAAK,CAAC,CAAC,EAAEJ,SAAS,GAAG,CAAC,CAAC,CAAC;cACjDA,SAAS,EAAE;YACb,CAAC,MAAM;cACLK,aAAa,CAACE,cAAc,CAAC;cAC7BhB,eAAe,CAAEiB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIhB,KAAK,CAACW,MAAM,CAAC;YACtD;UACF,CAAC,EAAE,EAAE,CAAC;QACR,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAME,aAAa,CAACJ,YAAY,CAAC;EAC1C,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;EAElB,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACrD,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACrD;EACF,CAAC;EAED,oBACE7B,OAAA;IAAS8B,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAElG/B,OAAA;MAAK8B,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1C/B,OAAA;QAAK8B,SAAS,EAAC;MAAsF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5GnC,OAAA;QAAK8B,SAAS,EAAC,kBAAkB;QAACM,KAAK,EAAE;UACvCC,eAAe,EAAE;AAC3B;AACA;AACA,WAAW;UACDC,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE;QACb;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,kCAAkC;MAAAC,QAAA,GAC9ClB,SAAS,CAAC2B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzB1C,OAAA,CAACZ,MAAM,CAACuD,GAAG;QAETb,SAAS,EAAC,2BAA2B;QACrCc,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCC,OAAO,EAAE;UACPF,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UACxBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,KAAK,EAAEX,KAAK,GAAG,CAAC;UAChBY,IAAI,EAAE;QACR,CAAE;QACFlB,KAAK,EAAE;UACLmB,GAAG,EAAE,GAAG,EAAE,GAAGb,KAAK,GAAG,EAAE,GAAG;UAC1Bc,IAAI,EAAE,GAAG,EAAE,GAAGd,KAAK,GAAG,EAAE;QAC1B,CAAE;QAAAX,QAAA,eAEF/B,OAAA,CAACyC,IAAI,CAAChC,IAAI;UAACgD,IAAI,EAAE;QAAG;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAnBlBM,IAAI,CAAC9B,KAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBL,CACb,CAAC,EAGD,CAAC,GAAGuB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAClB,GAAG,CAAC,CAACmB,CAAC,EAAEC,CAAC,kBACtB5D,OAAA,CAACZ,MAAM,CAACuD,GAAG;QAETb,SAAS,EAAC,6EAA6E;QACvFM,KAAK,EAAE;UACLoB,IAAI,EAAE,GAAG,EAAE,GAAGI,CAAC,GAAG,EAAE,GAAG;UACvBC,MAAM,EAAE;QACV,CAAE;QACFd,OAAO,EAAE;UACPe,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;UACtBjB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,CAAE;QACFI,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,KAAK,EAAEO,CAAC,GAAG,GAAG;UACdN,IAAI,EAAE;QACR;MAAE,GAfGM,CAAC;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnC,OAAA;MAAK8B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnE/B,OAAA;QAAK8B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAE1B/B,OAAA,CAACZ,MAAM,CAACuD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BpB,SAAS,EAAC,MAAM;UAAAC,QAAA,eAEhB/B,OAAA;YAAK8B,SAAS,EAAC,uFAAuF;YAAAC,QAAA,gBACpG/B,OAAA;cAAK8B,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC/B,OAAA;gBAAK8B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B/B,OAAA;kBAAK8B,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvDnC,OAAA;kBAAK8B,SAAS,EAAC;gBAAoC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DnC,OAAA;kBAAK8B,SAAS,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNnC,OAAA;gBAAM8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNnC,OAAA,CAACZ,MAAM,CAAC2E,CAAC;cACPnB,OAAO,EAAE;gBAAEoB,KAAK,EAAE;cAAE,CAAE;cACtBjB,OAAO,EAAE;gBAAEiB,KAAK,EAAE;cAAO,CAAE;cAC3Bf,UAAU,EAAE;gBAAEI,KAAK,EAAE,GAAG;gBAAEH,QAAQ,EAAE;cAAE,CAAE;cACxCpB,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACrE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEG,KAAK,EAAE;UAAI,CAAE;UAC1CvB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAGrB/B,OAAA,CAACZ,MAAM,CAAC6E,EAAE;YACRrB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE;YACpCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAE,CAAE;YAClCG,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE,CAAC;cAAEgB,IAAI,EAAE;YAAS,CAAE;YACxDpC,SAAS,EAAC,qFAAqF;YAC/F,aAAU,YAAY;YAAAC,QAAA,EACvB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAGZnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBAEtE/B,OAAA;cAAM8B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAAC,GAAG,eAC/CnC,OAAA;cAAM8B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE5B;YAAS;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClDnC,OAAA;cAAM8B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAGbnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,CAAC;cAAEH,QAAQ,EAAE;YAAI,CAAE;YACxCpB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAE7B/B,OAAA;cAAK8B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBACnD/B,OAAA;gBAAG8B,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,GAAC,WACtD,eAAA/B,OAAA;kBAAM8B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KACxF,eAAAnC,OAAA;kBAAM8B,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,SAC3E,eAAAnC,OAAA;kBAAM8B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,SACrF,eAAAnC,OAAA;kBAAM8B,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,mBAC7D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnC,OAAA;gBAAG8B,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGbnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,CAAC;cAAEH,QAAQ,EAAE;YAAI,CAAE;YACxCpB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAE7E/B,OAAA;cACEU,IAAI,EAAC,UAAU;cACfyD,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClB,MAAMC,cAAc,GAAG5C,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;gBACzD,IAAI2C,cAAc,EAAE;kBAClBA,cAAc,CAAC1C,cAAc,CAAC;oBAAEC,QAAQ,EAAE;kBAAS,CAAC,CAAC;gBACvD;cACF,CAAE;cACFC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEtD/B,OAAA,CAACP,IAAI;gBAACgE,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJnC,OAAA;cACEU,IAAI,EAAC,aAAa;cAClB6D,QAAQ;cACRzC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAExD/B,OAAA,CAACV,QAAQ;gBAACmE,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGbnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBI,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAE7CvB,WAAW,CAACgC,GAAG,CAAC,CAACgC,IAAI,EAAE9B,KAAK,kBAC3B1C,OAAA,CAACZ,MAAM,CAACqF,CAAC;cAEP7B,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAClCC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAClCG,UAAU,EAAE;gBAAEI,KAAK,EAAE,GAAG,GAAG,GAAG,GAAGX,KAAK;gBAAEQ,QAAQ,EAAE;cAAI,CAAE;cACxDxC,IAAI,EAAE8D,IAAI,CAAC9D,IAAK;cAChBgE,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB7C,SAAS,EAAC,kIAAkI;cAC5I,cAAY0C,IAAI,CAAC7D,KAAM;cAAAoB,QAAA,eAEvB/B,OAAA,CAACwE,IAAI,CAAC/D,IAAI;gBAACgD,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAVlBqC,IAAI,CAAC7D,KAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWP,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGbnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBI,UAAU,EAAE;YAAEI,KAAK,EAAE,GAAG;YAAEH,QAAQ,EAAE;UAAI,CAAE;UAC1CpB,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjE/B,OAAA;YACEmE,OAAO,EAAE3C,aAAc;YACvBM,SAAS,EAAC,oFAAoF;YAC9F,cAAW,yBAAyB;YAAAC,QAAA,eAEpC/B,OAAA,CAACX,WAAW;cAACoE,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;MACTI,OAAO,EAAE;QACPe,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACdd,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAClB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAEC,QAAQ;QAChBE,IAAI,EAAE;MACR,CAAE;MACFxB,SAAS,EAAC;IAAwE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,eAEFnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;MACTI,OAAO,EAAE;QACPe,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACbd,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MACnB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAEC,QAAQ;QAChBE,IAAI,EAAE,WAAW;QACjBD,KAAK,EAAE;MACT,CAAE;MACFvB,SAAS,EAAC;IAAgF;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEd,CAAC;AAACjC,EAAA,CApTID,IAAc;AAAA2E,EAAA,GAAd3E,IAAc;AAsTpB,eAAeA,IAAI;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}