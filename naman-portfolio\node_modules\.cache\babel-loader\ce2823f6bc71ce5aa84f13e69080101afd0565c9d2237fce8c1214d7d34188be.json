{"ast": null, "code": "/**\n * @license lucide-react v0.540.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14 13-8.381 8.38a1 1 0 0 1-3.001-3L11 9.999\",\n  key: \"1lw9ds\"\n}], [\"path\", {\n  d: \"M15.973 4.027A13 13 0 0 0 5.902 2.373c-1.398.342-1.092 2.158.277 2.601a19.9 19.9 0 0 1 5.822 3.024\",\n  key: \"ffj4ej\"\n}], [\"path\", {\n  d: \"M16.001 11.999a19.9 19.9 0 0 1 3.024 5.824c.444 1.369 2.26 1.676 2.603.278A13 13 0 0 0 20 8.069\",\n  key: \"8tj4zw\"\n}], [\"path\", {\n  d: \"M18.352 3.352a1.205 1.205 0 0 0-1.704 0l-5.296 5.296a1.205 1.205 0 0 0 0 1.704l2.296 2.296a1.205 1.205 0 0 0 1.704 0l5.296-5.296a1.205 1.205 0 0 0 0-1.704z\",\n  key: \"hh6h97\"\n}]];\nconst Pickaxe = createLucideIcon(\"pickaxe\", __iconNode);\nexport { __iconNode, Pickaxe as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Pickaxe", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\node_modules\\lucide-react\\src\\icons\\pickaxe.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm14 13-8.381 8.38a1 1 0 0 1-3.001-3L11 9.999', key: '1lw9ds' }],\n  [\n    'path',\n    {\n      d: 'M15.973 4.027A13 13 0 0 0 5.902 2.373c-1.398.342-1.092 2.158.277 2.601a19.9 19.9 0 0 1 5.822 3.024',\n      key: 'ffj4ej',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M16.001 11.999a19.9 19.9 0 0 1 3.024 5.824c.444 1.369 2.26 1.676 2.603.278A13 13 0 0 0 20 8.069',\n      key: '8tj4zw',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M18.352 3.352a1.205 1.205 0 0 0-1.704 0l-5.296 5.296a1.205 1.205 0 0 0 0 1.704l2.296 2.296a1.205 1.205 0 0 0 1.704 0l5.296-5.296a1.205 1.205 0 0 0 0-1.704z',\n      key: 'hh6h97',\n    },\n  ],\n];\n\n/**\n * @component @name Pickaxe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTQgMTMtOC4zODEgOC4zOGExIDEgMCAwIDEtMy4wMDEtM0wxMSA5Ljk5OSIgLz4KICA8cGF0aCBkPSJNMTUuOTczIDQuMDI3QTEzIDEzIDAgMCAwIDUuOTAyIDIuMzczYy0xLjM5OC4zNDItMS4wOTIgMi4xNTguMjc3IDIuNjAxYTE5LjkgMTkuOSAwIDAgMSA1LjgyMiAzLjAyNCIgLz4KICA8cGF0aCBkPSJNMTYuMDAxIDExLjk5OWExOS45IDE5LjkgMCAwIDEgMy4wMjQgNS44MjRjLjQ0NCAxLjM2OSAyLjI2IDEuNjc2IDIuNjAzLjI3OEExMyAxMyAwIDAgMCAyMCA4LjA2OSIgLz4KICA8cGF0aCBkPSJNMTguMzUyIDMuMzUyYTEuMjA1IDEuMjA1IDAgMCAwLTEuNzA0IDBsLTUuMjk2IDUuMjk2YTEuMjA1IDEuMjA1IDAgMCAwIDAgMS43MDRsMi4yOTYgMi4yOTZhMS4yMDUgMS4yMDUgMCAwIDAgMS43MDQgMGw1LjI5Ni01LjI5NmExLjIwNSAxLjIwNSAwIDAgMCAwLTEuNzA0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pickaxe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pickaxe = createLucideIcon('pickaxe', __iconNode);\n\nexport default Pickaxe;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAgDC,GAAA,EAAK;AAAA,CAAU,GAC7E,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EACP,CAEJ;AAaA,MAAMC,OAAA,GAAUC,gBAAA,CAAiB,WAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}