@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    color: #f8fafc;
    line-height: 1.6;
    overflow-x: hidden;
  }

  code {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #1e293b;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #06b6d4);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #0891b2);
  }

  /* Selection */
  ::selection {
    background: #3b82f6;
    color: white;
  }
}

@layer components {
  /* Professional gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #3b82f6, #06b6d4, #10b981);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 3s ease-in-out infinite;
    font-weight: 800;
  }

  /* Professional card effects */
  .card-hover {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(16px);
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 10px 40px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
  }

  /* Professional buttons */
  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #06b6d4);
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #0891b2);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
  }

  .btn-secondary {
    background: transparent;
    color: #3b82f6;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 8px;
    border: 2px solid #3b82f6;
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    background: #3b82f6;
    color: white;
    box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
  }
}

/* Advanced Keyframe Animations */
@keyframes matrix-bg {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(1deg); }
  50% { transform: translateY(0px) rotate(0deg); }
  75% { transform: translateY(10px) rotate(-1deg); }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes cyber-gradient {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

@keyframes holographic-shift {
  0%, 100% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
}

@keyframes scan-lines {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

@keyframes terminal-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes glow-rotate {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, 1px); }
  80% { transform: translate(1px, -1px); }
}

@keyframes glitch-2 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(1px, -1px); }
  40% { transform: translate(1px, 1px); }
  60% { transform: translate(-1px, -1px); }
  80% { transform: translate(-1px, 1px); }
}

@keyframes float-cyber {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-15px) translateX(5px); }
  50% { transform: translateY(0px) translateX(0px); }
  75% { transform: translateY(15px) translateX(-5px); }
}

@keyframes pulse-cyber {
  0%, 100% {
    box-shadow: 0 0 20px #00ffff30, 0 0 40px #00ffff20, 0 0 60px #00ffff10;
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px #00ffff50, 0 0 60px #00ffff30, 0 0 90px #00ffff20;
    transform: scale(1.02);
  }
}

@keyframes data-stream {
  0% { transform: translateY(-100%) scaleY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh) scaleY(1); opacity: 0; }
}

@keyframes circuit-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Utility Classes */
.float-cyber { animation: float-cyber 8s ease-in-out infinite; }
.pulse-cyber { animation: pulse-cyber 3s ease-in-out infinite; }
.data-stream { animation: data-stream 3s linear infinite; }
.circuit-pulse { animation: circuit-pulse 2s ease-in-out infinite; }

/* Mouse interaction effects */
.mouse-glow {
  position: relative;
  transition: all 0.3s ease;
}

.mouse-glow:hover {
  filter: brightness(1.2);
}

.mouse-glow::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, #00ffff30 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.mouse-glow:hover::after {
  width: 200px;
  height: 200px;
}
