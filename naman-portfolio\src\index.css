@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&family=Orbitron:wght@400;500;600;700;800;900&display=swap');

@layer base {
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: #000000;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
    cursor: none;
  }

  /* Matrix-style background */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 20% 50%, rgba(0, 255, 255, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(0, 255, 127, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(127, 0, 255, 0.03) 0%, transparent 50%),
      linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #000000 50%, #0a0a0a 75%, #000000 100%);
    z-index: -2;
    animation: matrix-bg 20s ease-in-out infinite;
  }

  /* Animated grid overlay */
  body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -1;
    animation: grid-move 30s linear infinite;
  }

  code {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
  }

  /* Custom cursor */
  .cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    border: 2px solid #00ffff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s ease;
    mix-blend-mode: difference;
  }

  .cursor-trail {
    position: fixed;
    width: 8px;
    height: 8px;
    background: #00ffff;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    opacity: 0.6;
  }

  /* Glitch effect */
  .glitch {
    position: relative;
    animation: glitch 2s infinite;
  }

  .glitch::before,
  .glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch::before {
    animation: glitch-1 0.5s infinite;
    color: #00ffff;
    z-index: -1;
  }

  .glitch::after {
    animation: glitch-2 0.5s infinite;
    color: #ff00ff;
    z-index: -2;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 12px;
  }

  ::-webkit-scrollbar-track {
    background: #000000;
    border-left: 1px solid #00ffff20;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00ffff, #0080ff);
    border-radius: 6px;
    border: 2px solid #000000;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00ffff, #ff00ff);
    box-shadow: 0 0 10px #00ffff;
  }

  /* Selection */
  ::selection {
    background: #00ffff30;
    color: #00ffff;
  }
}

@layer components {
  /* Cyber gradient text */
  .gradient-text {
    background: linear-gradient(45deg, #00ffff, #0080ff, #8000ff, #ff0080, #00ffff);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: cyber-gradient 4s ease-in-out infinite;
    font-family: 'Orbitron', monospace;
    font-weight: 800;
    text-shadow: 0 0 30px #00ffff50;
  }

  /* Neon card effects */
  .neon-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00ffff30;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .neon-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.6s;
  }

  .neon-card:hover::before {
    left: 100%;
  }

  .neon-card:hover {
    border-color: #00ffff;
    box-shadow:
      0 0 20px #00ffff30,
      0 0 40px #00ffff20,
      0 0 60px #00ffff10,
      inset 0 0 20px #00ffff05;
    transform: translateY(-5px) scale(1.02);
  }

  /* Cyber buttons */
  .btn-cyber {
    position: relative;
    background: transparent;
    border: 2px solid #00ffff;
    color: #00ffff;
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    padding: 12px 30px;
    text-transform: uppercase;
    letter-spacing: 2px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: none;
  }

  .btn-cyber::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, #00ffff20, transparent);
    transition: left 0.5s;
  }

  .btn-cyber:hover::before {
    left: 100%;
  }

  .btn-cyber:hover {
    color: #000000;
    background: #00ffff;
    box-shadow:
      0 0 20px #00ffff,
      0 0 40px #00ffff50,
      inset 0 0 20px #00ffff30;
    text-shadow: 0 0 10px #000000;
  }

  .btn-cyber-secondary {
    border-color: #8000ff;
    color: #8000ff;
  }

  .btn-cyber-secondary:hover {
    background: #8000ff;
    box-shadow:
      0 0 20px #8000ff,
      0 0 40px #8000ff50,
      inset 0 0 20px #8000ff30;
  }

  /* Matrix rain effect */
  .matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    opacity: 0.1;
  }

  /* Holographic effect */
  .holographic {
    background: linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.1) 50%, transparent 70%);
    background-size: 200% 200%;
    animation: holographic-shift 3s ease-in-out infinite;
  }

  /* Scan lines */
  .scan-lines::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 255, 0.03) 2px,
      rgba(0, 255, 255, 0.03) 4px
    );
    pointer-events: none;
    animation: scan-lines 2s linear infinite;
  }

  /* Terminal text effect */
  .terminal-text {
    font-family: 'JetBrains Mono', monospace;
    color: #00ff00;
    text-shadow: 0 0 10px #00ff0050;
    animation: terminal-blink 1s infinite;
  }

  /* Glowing border */
  .glow-border {
    position: relative;
  }

  .glow-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #0080ff, #8000ff, #ff0080, #00ffff);
    background-size: 400% 400%;
    border-radius: inherit;
    z-index: -1;
    animation: glow-rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .glow-border:hover::before {
    opacity: 1;
  }
}

/* Advanced Keyframe Animations */
@keyframes matrix-bg {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(1deg); }
  50% { transform: translateY(0px) rotate(0deg); }
  75% { transform: translateY(10px) rotate(-1deg); }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes cyber-gradient {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

@keyframes holographic-shift {
  0%, 100% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
}

@keyframes scan-lines {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

@keyframes terminal-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes glow-rotate {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, 1px); }
  80% { transform: translate(1px, -1px); }
}

@keyframes glitch-2 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(1px, -1px); }
  40% { transform: translate(1px, 1px); }
  60% { transform: translate(-1px, -1px); }
  80% { transform: translate(-1px, 1px); }
}

@keyframes float-cyber {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-15px) translateX(5px); }
  50% { transform: translateY(0px) translateX(0px); }
  75% { transform: translateY(15px) translateX(-5px); }
}

@keyframes pulse-cyber {
  0%, 100% {
    box-shadow: 0 0 20px #00ffff30, 0 0 40px #00ffff20, 0 0 60px #00ffff10;
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px #00ffff50, 0 0 60px #00ffff30, 0 0 90px #00ffff20;
    transform: scale(1.02);
  }
}

@keyframes data-stream {
  0% { transform: translateY(-100%) scaleY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh) scaleY(1); opacity: 0; }
}

@keyframes circuit-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* Utility Classes */
.float-cyber { animation: float-cyber 8s ease-in-out infinite; }
.pulse-cyber { animation: pulse-cyber 3s ease-in-out infinite; }
.data-stream { animation: data-stream 3s linear infinite; }
.circuit-pulse { animation: circuit-pulse 2s ease-in-out infinite; }

/* Mouse interaction effects */
.mouse-glow {
  position: relative;
  transition: all 0.3s ease;
}

.mouse-glow:hover {
  filter: brightness(1.2);
}

.mouse-glow::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, #00ffff30 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.mouse-glow:hover::after {
  width: 200px;
  height: 200px;
}
