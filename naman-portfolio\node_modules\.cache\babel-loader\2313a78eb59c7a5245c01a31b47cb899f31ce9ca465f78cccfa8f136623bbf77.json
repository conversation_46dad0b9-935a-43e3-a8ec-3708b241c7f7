{"ast": null, "code": "import { createEffect } from '../utils/create-effect.mjs';\nconst propEffect = /*@__PURE__*/createEffect((subject, state, key, value) => {\n  return state.set(key, value, () => {\n    subject[key] = state.latest[key];\n  }, undefined, false);\n});\nexport { propEffect };", "map": {"version": 3, "names": ["createEffect", "propEffect", "subject", "state", "key", "value", "set", "latest", "undefined"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/effects/prop/index.mjs"], "sourcesContent": ["import { createEffect } from '../utils/create-effect.mjs';\n\nconst propEffect = /*@__PURE__*/ createEffect((subject, state, key, value) => {\n    return state.set(key, value, () => {\n        subject[key] = state.latest[key];\n    }, undefined, false);\n});\n\nexport { propEffect };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AAEzD,MAAMC,UAAU,GAAG,aAAcD,YAAY,CAAC,CAACE,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,KAAK;EAC1E,OAAOF,KAAK,CAACG,GAAG,CAACF,GAAG,EAAEC,KAAK,EAAE,MAAM;IAC/BH,OAAO,CAACE,GAAG,CAAC,GAAGD,KAAK,CAACI,MAAM,CAACH,GAAG,CAAC;EACpC,CAAC,EAAEI,SAAS,EAAE,KAAK,CAAC;AACxB,CAAC,CAAC;AAEF,SAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}