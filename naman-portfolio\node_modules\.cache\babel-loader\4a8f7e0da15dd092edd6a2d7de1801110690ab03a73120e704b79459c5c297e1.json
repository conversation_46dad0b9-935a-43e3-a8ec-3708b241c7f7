{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Download, Github, Linkedin, Mail, ExternalLink, Terminal, Code, Cpu, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  _s();\n  const [typedText, setTypedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const roles = ['AI/ML Engineer', 'Full-Stack Developer', 'Automation Expert', 'Innovation Catalyst'];\n  const socialLinks = [{\n    icon: Github,\n    href: 'https://github.com/naman2002',\n    label: 'GitHub',\n    color: '#00ffff'\n  }, {\n    icon: Linkedin,\n    href: 'https://linkedin.com/in/naman-nagi-92026521',\n    label: 'LinkedIn',\n    color: '#0080ff'\n  }, {\n    icon: ExternalLink,\n    href: 'https://technaman.tech',\n    label: 'Website',\n    color: '#8000ff'\n  }, {\n    icon: Mail,\n    href: 'mailto:<EMAIL>',\n    label: 'Email',\n    color: '#ff0080'\n  }];\n  const techIcons = [{\n    icon: Terminal,\n    label: 'Terminal'\n  }, {\n    icon: Code,\n    label: 'Code'\n  }, {\n    icon: Cpu,\n    label: 'AI/ML'\n  }, {\n    icon: Zap,\n    label: 'Automation'\n  }];\n\n  // Typewriter effect\n  useEffect(() => {\n    const currentRole = roles[currentIndex];\n    let charIndex = 0;\n    const typeInterval = setInterval(() => {\n      if (charIndex < currentRole.length) {\n        setTypedText(currentRole.slice(0, charIndex + 1));\n        charIndex++;\n      } else {\n        clearInterval(typeInterval);\n        setTimeout(() => {\n          const deleteInterval = setInterval(() => {\n            if (charIndex > 0) {\n              setTypedText(currentRole.slice(0, charIndex - 1));\n              charIndex--;\n            } else {\n              clearInterval(deleteInterval);\n              setCurrentIndex(prev => (prev + 1) % roles.length);\n            }\n          }, 50);\n        }, 2000);\n      }\n    }, 100);\n    return () => clearInterval(typeInterval);\n  }, [currentIndex]);\n  const scrollToAbout = () => {\n    const aboutSection = document.querySelector('#about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"min-h-screen flex items-center justify-center relative overflow-hidden bg-black\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 opacity-20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0\",\n        style: {\n          backgroundImage: `\n            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px',\n          animation: 'grid-move 20s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [techIcons.map((tech, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute text-cyan-400/30\",\n        initial: {\n          opacity: 0,\n          scale: 0\n        },\n        animate: {\n          opacity: [0.3, 0.7, 0.3],\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 8,\n          repeat: Infinity,\n          delay: index * 2,\n          ease: \"easeInOut\"\n        },\n        style: {\n          top: `${20 + index * 20}%`,\n          left: `${10 + index * 20}%`\n        },\n        children: /*#__PURE__*/_jsxDEV(tech.icon, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this)\n      }, tech.label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this)), [...Array(6)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-px bg-gradient-to-b from-transparent via-cyan-400 to-transparent\",\n        style: {\n          left: `${15 + i * 15}%`,\n          height: '200px'\n        },\n        animate: {\n          y: ['-200px', '100vh'],\n          opacity: [0, 1, 0]\n        },\n        transition: {\n          duration: 3,\n          repeat: Infinity,\n          delay: i * 0.5,\n          ease: \"linear\"\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              delay: 0.2,\n              duration: 0.6\n            },\n            className: \"text-blue-400 text-xl font-semibold tracking-wide\",\n            children: \"\\uD83D\\uDC4B Hello, I'm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.4,\n              duration: 0.8\n            },\n            className: \"text-6xl sm:text-7xl lg:text-8xl font-black gradient-text leading-tight mb-4\",\n            children: \"Naman Nagi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.h2, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.6,\n              duration: 0.8\n            },\n            className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-200 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-400\",\n              children: \"AI/ML Engineer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), \" & \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-cyan-400\",\n              children: \"Full-Stack Developer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 71\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.8,\n              duration: 0.8\n            },\n            className: \"text-xl sm:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed font-medium\",\n            children: [\"Building \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-emerald-400 font-semibold\",\n              children: \"energy-efficient\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 24\n            }, this), \", \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-400 font-semibold\",\n              children: \"automated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 98\n            }, this), \", and \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-cyan-400 font-semibold\",\n              children: \"scalable AI solutions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 166\n            }, this), \" with 2+ years of experience. Passionate about sustainable impact in agriculture, environment monitoring, and smart infrastructure.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#contact\",\n              onClick: e => {\n                e.preventDefault();\n                const contactSection = document.querySelector('#contact');\n                if (contactSection) {\n                  contactSection.scrollIntoView({\n                    behavior: 'smooth'\n                  });\n                }\n              },\n              className: \"btn-primary inline-flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Mail, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), \"Get In Touch\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/resume.pdf\",\n              download: true,\n              className: \"btn-secondary inline-flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Download, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), \"Download Resume\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              delay: 1.2,\n              duration: 0.8\n            },\n            className: \"flex justify-center space-x-6 mt-8\",\n            children: socialLinks.map((link, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              initial: {\n                opacity: 0,\n                scale: 0\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                delay: 1.4 + 0.1 * index,\n                duration: 0.4\n              },\n              href: link.href,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"text-dark-400 hover:text-primary-400 transition-all duration-300 hover:scale-110 transform p-3 rounded-full hover:bg-dark-800/50\",\n              \"aria-label\": link.label,\n              children: /*#__PURE__*/_jsxDEV(link.icon, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, link.label, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            delay: 1.6,\n            duration: 0.8\n          },\n          className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: scrollToAbout,\n            className: \"text-dark-400 hover:text-primary-400 transition-colors duration-300 animate-bounce\",\n            \"aria-label\": \"Scroll to about section\",\n            children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n              size: 32\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      animate: {\n        y: [0, -20, 0],\n        rotate: [0, 5, 0]\n      },\n      transition: {\n        duration: 6,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      },\n      className: \"absolute top-20 left-10 w-20 h-20 bg-primary-500/10 rounded-lg blur-sm\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      animate: {\n        y: [0, 20, 0],\n        rotate: [0, -5, 0]\n      },\n      transition: {\n        duration: 8,\n        repeat: Infinity,\n        ease: \"easeInOut\",\n        delay: 1\n      },\n      className: \"absolute bottom-20 right-10 w-16 h-16 bg-secondary-500/10 rounded-full blur-sm\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s(Hero, \"0+ib4fLRCPqQo7iZy97ndy1TPeQ=\");\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "ChevronDown", "Download", "<PERSON><PERSON><PERSON>", "Linkedin", "Mail", "ExternalLink", "Terminal", "Code", "Cpu", "Zap", "jsxDEV", "_jsxDEV", "Hero", "_s", "typedText", "setTypedText", "currentIndex", "setCurrentIndex", "roles", "socialLinks", "icon", "href", "label", "color", "techIcons", "currentRole", "charIndex", "typeInterval", "setInterval", "length", "slice", "clearInterval", "setTimeout", "deleteInterval", "prev", "scrollToAbout", "aboutSection", "document", "querySelector", "scrollIntoView", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundImage", "backgroundSize", "animation", "map", "tech", "index", "div", "initial", "opacity", "scale", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "delay", "ease", "top", "left", "size", "Array", "_", "i", "height", "y", "p", "h1", "h2", "onClick", "e", "preventDefault", "contactSection", "download", "link", "a", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/sections/Hero.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Download, Github, Linkedin, Mail, ExternalLink, Terminal, Code, Cpu, Zap } from 'lucide-react';\n\nconst Hero: React.FC = () => {\n  const [typedText, setTypedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  const roles = [\n    'AI/ML Engineer',\n    'Full-Stack Developer',\n    'Automation Expert',\n    'Innovation Catalyst'\n  ];\n\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com/naman2002', label: 'GitHub', color: '#00ffff' },\n    { icon: Linkedin, href: 'https://linkedin.com/in/naman-nagi-92026521', label: 'LinkedIn', color: '#0080ff' },\n    { icon: ExternalLink, href: 'https://technaman.tech', label: 'Website', color: '#8000ff' },\n    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email', color: '#ff0080' },\n  ];\n\n  const techIcons = [\n    { icon: Terminal, label: 'Terminal' },\n    { icon: Code, label: 'Code' },\n    { icon: Cpu, label: 'AI/ML' },\n    { icon: Zap, label: 'Automation' },\n  ];\n\n  // Typewriter effect\n  useEffect(() => {\n    const currentRole = roles[currentIndex];\n    let charIndex = 0;\n\n    const typeInterval = setInterval(() => {\n      if (charIndex < currentRole.length) {\n        setTypedText(currentRole.slice(0, charIndex + 1));\n        charIndex++;\n      } else {\n        clearInterval(typeInterval);\n        setTimeout(() => {\n          const deleteInterval = setInterval(() => {\n            if (charIndex > 0) {\n              setTypedText(currentRole.slice(0, charIndex - 1));\n              charIndex--;\n            } else {\n              clearInterval(deleteInterval);\n              setCurrentIndex((prev) => (prev + 1) % roles.length);\n            }\n          }, 50);\n        }, 2000);\n      }\n    }, 100);\n\n    return () => clearInterval(typeInterval);\n  }, [currentIndex]);\n\n  const scrollToAbout = () => {\n    const aboutSection = document.querySelector('#about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-black\">\n      {/* Cyber Grid Background */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-purple-500/10\"></div>\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `\n            linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '50px 50px',\n          animation: 'grid-move 20s linear infinite'\n        }}></div>\n      </div>\n\n      {/* Floating Tech Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {techIcons.map((tech, index) => (\n          <motion.div\n            key={tech.label}\n            className=\"absolute text-cyan-400/30\"\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{\n              opacity: [0.3, 0.7, 0.3],\n              scale: [1, 1.2, 1],\n              rotate: [0, 180, 360]\n            }}\n            transition={{\n              duration: 8,\n              repeat: Infinity,\n              delay: index * 2,\n              ease: \"easeInOut\"\n            }}\n            style={{\n              top: `${20 + index * 20}%`,\n              left: `${10 + index * 20}%`,\n            }}\n          >\n            <tech.icon size={40} />\n          </motion.div>\n        ))}\n\n        {/* Data streams */}\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-px bg-gradient-to-b from-transparent via-cyan-400 to-transparent\"\n            style={{\n              left: `${15 + i * 15}%`,\n              height: '200px',\n            }}\n            animate={{\n              y: ['-200px', '100vh'],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: 3,\n              repeat: Infinity,\n              delay: i * 0.5,\n              ease: \"linear\"\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"text-center\">\n          {/* Main Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-6\"\n          >\n            {/* Greeting */}\n            <motion.p\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.2, duration: 0.6 }}\n              className=\"text-blue-400 text-xl font-semibold tracking-wide\"\n            >\n              👋 Hello, I'm\n            </motion.p>\n\n            {/* Name */}\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4, duration: 0.8 }}\n              className=\"text-6xl sm:text-7xl lg:text-8xl font-black gradient-text leading-tight mb-4\"\n            >\n              Naman Nagi\n            </motion.h1>\n\n            {/* Title */}\n            <motion.h2\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6, duration: 0.8 }}\n              className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-slate-200 mb-6\"\n            >\n              <span className=\"text-blue-400\">AI/ML Engineer</span> & <span className=\"text-cyan-400\">Full-Stack Developer</span>\n            </motion.h2>\n\n            {/* Description */}\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.8 }}\n              className=\"text-xl sm:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed font-medium\"\n            >\n              Building <span className=\"text-emerald-400 font-semibold\">energy-efficient</span>, <span className=\"text-blue-400 font-semibold\">automated</span>, and <span className=\"text-cyan-400 font-semibold\">scalable AI solutions</span> with 2+ years of experience.\n              Passionate about sustainable impact in agriculture, environment monitoring, and smart infrastructure.\n            </motion.p>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.8 }}\n              className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mt-12\"\n            >\n              <a\n                href=\"#contact\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  const contactSection = document.querySelector('#contact');\n                  if (contactSection) {\n                    contactSection.scrollIntoView({ behavior: 'smooth' });\n                  }\n                }}\n                className=\"btn-primary inline-flex items-center gap-2\"\n              >\n                <Mail size={20} />\n                Get In Touch\n              </a>\n              \n              <a\n                href=\"/resume.pdf\"\n                download\n                className=\"btn-secondary inline-flex items-center gap-2\"\n              >\n                <Download size={20} />\n                Download Resume\n              </a>\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 1.2, duration: 0.8 }}\n              className=\"flex justify-center space-x-6 mt-8\"\n            >\n              {socialLinks.map((link, index) => (\n                <motion.a\n                  key={link.label}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.4 + 0.1 * index, duration: 0.4 }}\n                  href={link.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-dark-400 hover:text-primary-400 transition-all duration-300 hover:scale-110 transform p-3 rounded-full hover:bg-dark-800/50\"\n                  aria-label={link.label}\n                >\n                  <link.icon size={24} />\n                </motion.a>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Scroll Indicator */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 1.6, duration: 0.8 }}\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          >\n            <button\n              onClick={scrollToAbout}\n              className=\"text-dark-400 hover:text-primary-400 transition-colors duration-300 animate-bounce\"\n              aria-label=\"Scroll to about section\"\n            >\n              <ChevronDown size={32} />\n            </button>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Floating Elements */}\n      <motion.div\n        animate={{\n          y: [0, -20, 0],\n          rotate: [0, 5, 0],\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n        }}\n        className=\"absolute top-20 left-10 w-20 h-20 bg-primary-500/10 rounded-lg blur-sm\"\n      />\n      \n      <motion.div\n        animate={{\n          y: [0, 20, 0],\n          rotate: [0, -5, 0],\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1,\n        }}\n        className=\"absolute bottom-20 right-10 w-16 h-16 bg-secondary-500/10 rounded-full blur-sm\"\n      />\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErH,MAAMC,IAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAMoB,KAAK,GAAG,CACZ,gBAAgB,EAChB,sBAAsB,EACtB,mBAAmB,EACnB,qBAAqB,CACtB;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEC,IAAI,EAAElB,MAAM;IAAEmB,IAAI,EAAE,8BAA8B;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzF;IAAEH,IAAI,EAAEjB,QAAQ;IAAEkB,IAAI,EAAE,6CAA6C;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5G;IAAEH,IAAI,EAAEf,YAAY;IAAEgB,IAAI,EAAE,wBAAwB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC1F;IAAEH,IAAI,EAAEhB,IAAI;IAAEiB,IAAI,EAAE,gCAAgC;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,CACzF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEJ,IAAI,EAAEd,QAAQ;IAAEgB,KAAK,EAAE;EAAW,CAAC,EACrC;IAAEF,IAAI,EAAEb,IAAI;IAAEe,KAAK,EAAE;EAAO,CAAC,EAC7B;IAAEF,IAAI,EAAEZ,GAAG;IAAEc,KAAK,EAAE;EAAQ,CAAC,EAC7B;IAAEF,IAAI,EAAEX,GAAG;IAAEa,KAAK,EAAE;EAAa,CAAC,CACnC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM4B,WAAW,GAAGP,KAAK,CAACF,YAAY,CAAC;IACvC,IAAIU,SAAS,GAAG,CAAC;IAEjB,MAAMC,YAAY,GAAGC,WAAW,CAAC,MAAM;MACrC,IAAIF,SAAS,GAAGD,WAAW,CAACI,MAAM,EAAE;QAClCd,YAAY,CAACU,WAAW,CAACK,KAAK,CAAC,CAAC,EAAEJ,SAAS,GAAG,CAAC,CAAC,CAAC;QACjDA,SAAS,EAAE;MACb,CAAC,MAAM;QACLK,aAAa,CAACJ,YAAY,CAAC;QAC3BK,UAAU,CAAC,MAAM;UACf,MAAMC,cAAc,GAAGL,WAAW,CAAC,MAAM;YACvC,IAAIF,SAAS,GAAG,CAAC,EAAE;cACjBX,YAAY,CAACU,WAAW,CAACK,KAAK,CAAC,CAAC,EAAEJ,SAAS,GAAG,CAAC,CAAC,CAAC;cACjDA,SAAS,EAAE;YACb,CAAC,MAAM;cACLK,aAAa,CAACE,cAAc,CAAC;cAC7BhB,eAAe,CAAEiB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIhB,KAAK,CAACW,MAAM,CAAC;YACtD;UACF,CAAC,EAAE,EAAE,CAAC;QACR,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAME,aAAa,CAACJ,YAAY,CAAC;EAC1C,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;EAElB,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACrD,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACG,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACrD;EACF,CAAC;EAED,oBACE7B,OAAA;IAAS8B,SAAS,EAAC,iFAAiF;IAAAC,QAAA,gBAElG/B,OAAA;MAAK8B,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1C/B,OAAA;QAAK8B,SAAS,EAAC;MAAsF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5GnC,OAAA;QAAK8B,SAAS,EAAC,kBAAkB;QAACM,KAAK,EAAE;UACvCC,eAAe,EAAE;AAC3B;AACA;AACA,WAAW;UACDC,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE;QACb;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnC,OAAA;MAAK8B,SAAS,EAAC,kCAAkC;MAAAC,QAAA,GAC9ClB,SAAS,CAAC2B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzB1C,OAAA,CAACZ,MAAM,CAACuD,GAAG;QAETb,SAAS,EAAC,2BAA2B;QACrCc,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCC,OAAO,EAAE;UACPF,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UACxBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,KAAK,EAAEX,KAAK,GAAG,CAAC;UAChBY,IAAI,EAAE;QACR,CAAE;QACFlB,KAAK,EAAE;UACLmB,GAAG,EAAE,GAAG,EAAE,GAAGb,KAAK,GAAG,EAAE,GAAG;UAC1Bc,IAAI,EAAE,GAAG,EAAE,GAAGd,KAAK,GAAG,EAAE;QAC1B,CAAE;QAAAX,QAAA,eAEF/B,OAAA,CAACyC,IAAI,CAAChC,IAAI;UAACgD,IAAI,EAAE;QAAG;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAnBlBM,IAAI,CAAC9B,KAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBL,CACb,CAAC,EAGD,CAAC,GAAGuB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAClB,GAAG,CAAC,CAACmB,CAAC,EAAEC,CAAC,kBACtB5D,OAAA,CAACZ,MAAM,CAACuD,GAAG;QAETb,SAAS,EAAC,6EAA6E;QACvFM,KAAK,EAAE;UACLoB,IAAI,EAAE,GAAG,EAAE,GAAGI,CAAC,GAAG,EAAE,GAAG;UACvBC,MAAM,EAAE;QACV,CAAE;QACFd,OAAO,EAAE;UACPe,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;UACtBjB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,CAAE;QACFI,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,KAAK,EAAEO,CAAC,GAAG,GAAG;UACdN,IAAI,EAAE;QACR;MAAE,GAfGM,CAAC;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnC,OAAA;MAAK8B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnE/B,OAAA;QAAK8B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAE1B/B,OAAA,CAACZ,MAAM,CAACuD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAG,CAAE;UAC/Bf,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEiB,CAAC,EAAE;UAAE,CAAE;UAC9Bb,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BpB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAGrB/B,OAAA,CAACZ,MAAM,CAAC2E,CAAC;YACPnB,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBI,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAC9D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAGXnC,OAAA,CAACZ,MAAM,CAAC4E,EAAE;YACRpB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,8EAA8E;YAAAC,QAAA,EACzF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAGZnC,OAAA,CAACZ,MAAM,CAAC6E,EAAE;YACRrB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAE1E/B,OAAA;cAAM8B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,OAAG,eAAAnC,OAAA;cAAM8B,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGZnC,OAAA,CAACZ,MAAM,CAAC2E,CAAC;YACPnB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,kFAAkF;YAAAC,QAAA,GAC7F,WACU,eAAA/B,OAAA;cAAM8B,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,MAAE,eAAAnC,OAAA;cAAM8B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,UAAM,eAAAnC,OAAA;cAAM8B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,uIAEnO;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAGXnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAG,CAAE;YAC/Bf,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEiB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEI,KAAK,EAAE,CAAC;cAAEH,QAAQ,EAAE;YAAI,CAAE;YACxCpB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAE7E/B,OAAA;cACEU,IAAI,EAAC,UAAU;cACfwD,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClB,MAAMC,cAAc,GAAG3C,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;gBACzD,IAAI0C,cAAc,EAAE;kBAClBA,cAAc,CAACzC,cAAc,CAAC;oBAAEC,QAAQ,EAAE;kBAAS,CAAC,CAAC;gBACvD;cACF,CAAE;cACFC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEtD/B,OAAA,CAACP,IAAI;gBAACgE,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJnC,OAAA;cACEU,IAAI,EAAC,aAAa;cAClB4D,QAAQ;cACRxC,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAExD/B,OAAA,CAACV,QAAQ;gBAACmE,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAExB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGbnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBE,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBI,UAAU,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,QAAQ,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAE7CvB,WAAW,CAACgC,GAAG,CAAC,CAAC+B,IAAI,EAAE7B,KAAK,kBAC3B1C,OAAA,CAACZ,MAAM,CAACoF,CAAC;cAEP5B,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAClCC,OAAO,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,KAAK,EAAE;cAAE,CAAE;cAClCG,UAAU,EAAE;gBAAEI,KAAK,EAAE,GAAG,GAAG,GAAG,GAAGX,KAAK;gBAAEQ,QAAQ,EAAE;cAAI,CAAE;cACxDxC,IAAI,EAAE6D,IAAI,CAAC7D,IAAK;cAChB+D,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzB5C,SAAS,EAAC,kIAAkI;cAC5I,cAAYyC,IAAI,CAAC5D,KAAM;cAAAoB,QAAA,eAEvB/B,OAAA,CAACuE,IAAI,CAAC9D,IAAI;gBAACgD,IAAI,EAAE;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAVlBoC,IAAI,CAAC5D,KAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWP,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGbnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBI,UAAU,EAAE;YAAEI,KAAK,EAAE,GAAG;YAAEH,QAAQ,EAAE;UAAI,CAAE;UAC1CpB,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjE/B,OAAA;YACEkE,OAAO,EAAE1C,aAAc;YACvBM,SAAS,EAAC,oFAAoF;YAC9F,cAAW,yBAAyB;YAAAC,QAAA,eAEpC/B,OAAA,CAACX,WAAW;cAACoE,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;MACTI,OAAO,EAAE;QACPe,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACdd,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAClB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAEC,QAAQ;QAChBE,IAAI,EAAE;MACR,CAAE;MACFxB,SAAS,EAAC;IAAwE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,eAEFnC,OAAA,CAACZ,MAAM,CAACuD,GAAG;MACTI,OAAO,EAAE;QACPe,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACbd,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MACnB,CAAE;MACFC,UAAU,EAAE;QACVC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAEC,QAAQ;QAChBE,IAAI,EAAE,WAAW;QACjBD,KAAK,EAAE;MACT,CAAE;MACFvB,SAAS,EAAC;IAAgF;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEd,CAAC;AAACjC,EAAA,CAvRID,IAAc;AAAA0E,EAAA,GAAd1E,IAAc;AAyRpB,eAAeA,IAAI;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}