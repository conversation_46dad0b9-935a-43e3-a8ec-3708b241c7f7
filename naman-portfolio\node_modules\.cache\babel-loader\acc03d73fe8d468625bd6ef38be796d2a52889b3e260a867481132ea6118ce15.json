{"ast": null, "code": "import { isMotionValue } from 'motion-dom';\nimport { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n  const {\n    style\n  } = props;\n  const newValues = {};\n  for (const key in style) {\n    if (isMotionValue(style[key]) || prevProps.style && isMotionValue(prevProps.style[key]) || isForcedMotionValue(key, props) || visualElement?.getValue(key)?.liveStyle !== undefined) {\n      newValues[key] = style[key];\n    }\n  }\n  return newValues;\n}\nexport { scrapeMotionValuesFromProps };", "map": {"version": 3, "names": ["isMotionValue", "isForcedMotionValue", "scrapeMotionValuesFromProps", "props", "prevProps", "visualElement", "style", "newValues", "key", "getValue", "liveStyle", "undefined"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if (isMotionValue(style[key]) ||\n            (prevProps.style &&\n                isMotionValue(prevProps.style[key])) ||\n            isForcedMotionValue(key, props) ||\n            visualElement?.getValue(key)?.liveStyle !== undefined) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAY;AAC1C,SAASC,mBAAmB,QAAQ,kDAAkD;AAEtF,SAASC,2BAA2BA,CAACC,KAAK,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAClE,MAAM;IAAEC;EAAM,CAAC,GAAGH,KAAK;EACvB,MAAMI,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,MAAMC,GAAG,IAAIF,KAAK,EAAE;IACrB,IAAIN,aAAa,CAACM,KAAK,CAACE,GAAG,CAAC,CAAC,IACxBJ,SAAS,CAACE,KAAK,IACZN,aAAa,CAACI,SAAS,CAACE,KAAK,CAACE,GAAG,CAAC,CAAE,IACxCP,mBAAmB,CAACO,GAAG,EAAEL,KAAK,CAAC,IAC/BE,aAAa,EAAEI,QAAQ,CAACD,GAAG,CAAC,EAAEE,SAAS,KAAKC,SAAS,EAAE;MACvDJ,SAAS,CAACC,GAAG,CAAC,GAAGF,KAAK,CAACE,GAAG,CAAC;IAC/B;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA,SAASL,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}