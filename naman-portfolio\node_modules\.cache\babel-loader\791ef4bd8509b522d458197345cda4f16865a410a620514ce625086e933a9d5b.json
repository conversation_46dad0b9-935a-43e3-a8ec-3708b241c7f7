{"ast": null, "code": "import { frame, cancelFrame } from '../frameloop/frame.mjs';\nimport { numberValueTypes } from '../value/types/maps/number.mjs';\nimport { getValueAsType } from '../value/types/utils/get-as-type.mjs';\nclass MotionValueState {\n  constructor() {\n    this.latest = {};\n    this.values = new Map();\n  }\n  set(name, value, render, computed, useDefaultValueType = true) {\n    const existingValue = this.values.get(name);\n    if (existingValue) {\n      existingValue.onRemove();\n    }\n    const onChange = () => {\n      const v = value.get();\n      if (useDefaultValueType) {\n        this.latest[name] = getValueAsType(v, numberValueTypes[name]);\n      } else {\n        this.latest[name] = v;\n      }\n      render && frame.render(render);\n    };\n    onChange();\n    const cancelOnChange = value.on(\"change\", onChange);\n    computed && value.addDependent(computed);\n    const remove = () => {\n      cancelOnChange();\n      render && cancelFrame(render);\n      this.values.delete(name);\n      computed && value.removeDependent(computed);\n    };\n    this.values.set(name, {\n      value,\n      onRemove: remove\n    });\n    return remove;\n  }\n  get(name) {\n    return this.values.get(name)?.value;\n  }\n  destroy() {\n    for (const value of this.values.values()) {\n      value.onRemove();\n    }\n  }\n}\nexport { MotionValueState };", "map": {"version": 3, "names": ["frame", "cancelFrame", "numberValueTypes", "getValueAsType", "MotionValueState", "constructor", "latest", "values", "Map", "set", "name", "value", "render", "computed", "useDefaultValueType", "existingValue", "get", "onRemove", "onChange", "v", "cancelOnChange", "on", "addDependent", "remove", "delete", "removeDependent", "destroy"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/effects/MotionValueState.mjs"], "sourcesContent": ["import { frame, cancelFrame } from '../frameloop/frame.mjs';\nimport { numberValueTypes } from '../value/types/maps/number.mjs';\nimport { getValueAsType } from '../value/types/utils/get-as-type.mjs';\n\nclass MotionValueState {\n    constructor() {\n        this.latest = {};\n        this.values = new Map();\n    }\n    set(name, value, render, computed, useDefaultValueType = true) {\n        const existingValue = this.values.get(name);\n        if (existingValue) {\n            existingValue.onRemove();\n        }\n        const onChange = () => {\n            const v = value.get();\n            if (useDefaultValueType) {\n                this.latest[name] = getValueAsType(v, numberValueTypes[name]);\n            }\n            else {\n                this.latest[name] = v;\n            }\n            render && frame.render(render);\n        };\n        onChange();\n        const cancelOnChange = value.on(\"change\", onChange);\n        computed && value.addDependent(computed);\n        const remove = () => {\n            cancelOnChange();\n            render && cancelFrame(render);\n            this.values.delete(name);\n            computed && value.removeDependent(computed);\n        };\n        this.values.set(name, { value, onRemove: remove });\n        return remove;\n    }\n    get(name) {\n        return this.values.get(name)?.value;\n    }\n    destroy() {\n        for (const value of this.values.values()) {\n            value.onRemove();\n        }\n    }\n}\n\nexport { MotionValueState };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,cAAc,QAAQ,sCAAsC;AAErE,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B;EACAC,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,mBAAmB,GAAG,IAAI,EAAE;IAC3D,MAAMC,aAAa,GAAG,IAAI,CAACR,MAAM,CAACS,GAAG,CAACN,IAAI,CAAC;IAC3C,IAAIK,aAAa,EAAE;MACfA,aAAa,CAACE,QAAQ,CAAC,CAAC;IAC5B;IACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnB,MAAMC,CAAC,GAAGR,KAAK,CAACK,GAAG,CAAC,CAAC;MACrB,IAAIF,mBAAmB,EAAE;QACrB,IAAI,CAACR,MAAM,CAACI,IAAI,CAAC,GAAGP,cAAc,CAACgB,CAAC,EAAEjB,gBAAgB,CAACQ,IAAI,CAAC,CAAC;MACjE,CAAC,MACI;QACD,IAAI,CAACJ,MAAM,CAACI,IAAI,CAAC,GAAGS,CAAC;MACzB;MACAP,MAAM,IAAIZ,KAAK,CAACY,MAAM,CAACA,MAAM,CAAC;IAClC,CAAC;IACDM,QAAQ,CAAC,CAAC;IACV,MAAME,cAAc,GAAGT,KAAK,CAACU,EAAE,CAAC,QAAQ,EAAEH,QAAQ,CAAC;IACnDL,QAAQ,IAAIF,KAAK,CAACW,YAAY,CAACT,QAAQ,CAAC;IACxC,MAAMU,MAAM,GAAGA,CAAA,KAAM;MACjBH,cAAc,CAAC,CAAC;MAChBR,MAAM,IAAIX,WAAW,CAACW,MAAM,CAAC;MAC7B,IAAI,CAACL,MAAM,CAACiB,MAAM,CAACd,IAAI,CAAC;MACxBG,QAAQ,IAAIF,KAAK,CAACc,eAAe,CAACZ,QAAQ,CAAC;IAC/C,CAAC;IACD,IAAI,CAACN,MAAM,CAACE,GAAG,CAACC,IAAI,EAAE;MAAEC,KAAK;MAAEM,QAAQ,EAAEM;IAAO,CAAC,CAAC;IAClD,OAAOA,MAAM;EACjB;EACAP,GAAGA,CAACN,IAAI,EAAE;IACN,OAAO,IAAI,CAACH,MAAM,CAACS,GAAG,CAACN,IAAI,CAAC,EAAEC,KAAK;EACvC;EACAe,OAAOA,CAAA,EAAG;IACN,KAAK,MAAMf,KAAK,IAAI,IAAI,CAACJ,MAAM,CAACA,MAAM,CAAC,CAAC,EAAE;MACtCI,KAAK,CAACM,QAAQ,CAAC,CAAC;IACpB;EACJ;AACJ;AAEA,SAASb,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}