{"ast": null, "code": "import { observeTimeline } from 'motion-dom';\nimport { scrollInfo } from './track.mjs';\nimport { getTimeline } from './utils/get-timeline.mjs';\n\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n  return onScroll.length === 2;\n}\nfunction attachToFunction(onScroll, options) {\n  if (isOnScrollWithInfo(onScroll)) {\n    return scrollInfo(info => {\n      onScroll(info[options.axis].progress, info);\n    }, options);\n  } else {\n    return observeTimeline(onScroll, getTimeline(options));\n  }\n}\nexport { attachToFunction };", "map": {"version": 3, "names": ["observeTimeline", "scrollInfo", "getTimeline", "isOnScrollWithInfo", "onScroll", "length", "attachToFunction", "options", "info", "axis", "progress"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs"], "sourcesContent": ["import { observeTimeline } from 'motion-dom';\nimport { scrollInfo } from './track.mjs';\nimport { getTimeline } from './utils/get-timeline.mjs';\n\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\nfunction attachToFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll)) {\n        return scrollInfo((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return observeTimeline(onScroll, getTimeline(options));\n    }\n}\n\nexport { attachToFunction };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,YAAY;AAC5C,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,WAAW,QAAQ,0BAA0B;;AAEtD;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,QAAQ,EAAE;EAClC,OAAOA,QAAQ,CAACC,MAAM,KAAK,CAAC;AAChC;AACA,SAASC,gBAAgBA,CAACF,QAAQ,EAAEG,OAAO,EAAE;EACzC,IAAIJ,kBAAkB,CAACC,QAAQ,CAAC,EAAE;IAC9B,OAAOH,UAAU,CAAEO,IAAI,IAAK;MACxBJ,QAAQ,CAACI,IAAI,CAACD,OAAO,CAACE,IAAI,CAAC,CAACC,QAAQ,EAAEF,IAAI,CAAC;IAC/C,CAAC,EAAED,OAAO,CAAC;EACf,CAAC,MACI;IACD,OAAOP,eAAe,CAACI,QAAQ,EAAEF,WAAW,CAACK,OAAO,CAAC,CAAC;EAC1D;AACJ;AAEA,SAASD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}