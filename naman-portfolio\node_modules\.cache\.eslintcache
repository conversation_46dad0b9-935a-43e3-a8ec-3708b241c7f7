[{"C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Projects.tsx": "4", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Experience.tsx": "5", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Hero.tsx": "6", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Contact.tsx": "7", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\About.tsx": "8", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Skills.tsx": "9", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Education.tsx": "10", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Achievements.tsx": "11", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Header.tsx": "13", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Footer.tsx": "14", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\data\\portfolio.ts": "15", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\MatrixRain.tsx": "16", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\CustomCursor.tsx": "17"}, {"size": 554, "mtime": 1755529161888, "results": "18", "hashOfConfig": "19"}, {"size": 425, "mtime": 1755529161620, "results": "20", "hashOfConfig": "19"}, {"size": 743, "mtime": 1755530135741, "results": "21", "hashOfConfig": "19"}, {"size": 10228, "mtime": 1755529941614, "results": "22", "hashOfConfig": "19"}, {"size": 6622, "mtime": 1755529882260, "results": "23", "hashOfConfig": "19"}, {"size": 14032, "mtime": 1755532138902, "results": "24", "hashOfConfig": "19"}, {"size": 11000, "mtime": 1755530121175, "results": "25", "hashOfConfig": "19"}, {"size": 7267, "mtime": 1755531533766, "results": "26", "hashOfConfig": "19"}, {"size": 6839, "mtime": 1755529837756, "results": "27", "hashOfConfig": "19"}, {"size": 8816, "mtime": 1755529996180, "results": "28", "hashOfConfig": "19"}, {"size": 10022, "mtime": 1755530961070, "results": "29", "hashOfConfig": "19"}, {"size": 560, "mtime": 1755532052975, "results": "30", "hashOfConfig": "19"}, {"size": 6315, "mtime": 1755532112007, "results": "31", "hashOfConfig": "19"}, {"size": 5978, "mtime": 1755529688470, "results": "32", "hashOfConfig": "19"}, {"size": 14192, "mtime": 1755529614722, "results": "33", "hashOfConfig": "19"}, {"size": 2074, "mtime": 1755531869878, "results": "34", "hashOfConfig": "19"}, {"size": 3717, "mtime": 1755531850825, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f8hrgb", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Projects.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Experience.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Hero.tsx", ["87"], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Skills.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Education.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Achievements.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\data\\portfolio.ts", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\MatrixRain.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\CustomCursor.tsx", [], [], {"ruleId": "88", "severity": 1, "message": "89", "line": 9, "column": 9, "nodeType": "90", "endLine": 14, "endColumn": 4}, "react-hooks/exhaustive-deps", "The 'roles' array makes the dependencies of useEffect Hook (at line 56) change on every render. Move it inside the useEffect callback. Alternatively, wrap the initialization of 'roles' in its own useMemo() Hook.", "VariableDeclarator"]