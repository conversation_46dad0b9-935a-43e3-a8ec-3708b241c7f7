[{"C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Projects.tsx": "4", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Experience.tsx": "5", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Hero.tsx": "6", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Contact.tsx": "7", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\About.tsx": "8", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Skills.tsx": "9", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Education.tsx": "10", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Achievements.tsx": "11", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Header.tsx": "13", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Footer.tsx": "14", "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\data\\portfolio.ts": "15"}, {"size": 554, "mtime": 1755529161888, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1755529161620, "results": "18", "hashOfConfig": "17"}, {"size": 743, "mtime": 1755530135741, "results": "19", "hashOfConfig": "17"}, {"size": 10228, "mtime": 1755529941614, "results": "20", "hashOfConfig": "17"}, {"size": 6622, "mtime": 1755529882260, "results": "21", "hashOfConfig": "17"}, {"size": 7778, "mtime": 1755531551259, "results": "22", "hashOfConfig": "17"}, {"size": 11000, "mtime": 1755530121175, "results": "23", "hashOfConfig": "17"}, {"size": 7267, "mtime": 1755531533766, "results": "24", "hashOfConfig": "17"}, {"size": 6839, "mtime": 1755529837756, "results": "25", "hashOfConfig": "17"}, {"size": 8816, "mtime": 1755529996180, "results": "26", "hashOfConfig": "17"}, {"size": 10022, "mtime": 1755530961070, "results": "27", "hashOfConfig": "17"}, {"size": 416, "mtime": 1755529697983, "results": "28", "hashOfConfig": "17"}, {"size": 6082, "mtime": 1755531474623, "results": "29", "hashOfConfig": "17"}, {"size": 5978, "mtime": 1755529688470, "results": "30", "hashOfConfig": "17"}, {"size": 14192, "mtime": 1755529614722, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f8hrgb", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Projects.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Experience.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Skills.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Education.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\sections\\Achievements.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\naman's portfolio\\naman-portfolio\\src\\data\\portfolio.ts", [], []]