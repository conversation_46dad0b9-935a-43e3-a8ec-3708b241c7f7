{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\naman's portfolio\\\\naman-portfolio\\\\src\\\\components\\\\layout\\\\CustomCursor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CustomCursor = () => {\n  _s();\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isHovering, setIsHovering] = useState(false);\n  const [trails, setTrails] = useState([]);\n  useEffect(() => {\n    let trailId = 0;\n    const updateMousePosition = e => {\n      const newPosition = {\n        x: e.clientX,\n        y: e.clientY\n      };\n      setMousePosition(newPosition);\n\n      // Add trail\n      setTrails(prev => {\n        const newTrail = {\n          ...newPosition,\n          id: trailId++\n        };\n        const updatedTrails = [newTrail, ...prev.slice(0, 8)];\n        return updatedTrails;\n      });\n    };\n    const handleMouseEnter = () => setIsHovering(true);\n    const handleMouseLeave = () => setIsHovering(false);\n\n    // Add event listeners to interactive elements\n    const interactiveElements = document.querySelectorAll('a, button, [role=\"button\"], input, textarea, select');\n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter);\n      el.addEventListener('mouseleave', handleMouseLeave);\n    });\n    window.addEventListener('mousemove', updateMousePosition);\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition);\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter);\n        el.removeEventListener('mouseleave', handleMouseLeave);\n      });\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"fixed pointer-events-none z-[9999] mix-blend-mode-difference\",\n      animate: {\n        x: mousePosition.x - 10,\n        y: mousePosition.y - 10,\n        scale: isHovering ? 1.5 : 1\n      },\n      transition: {\n        type: \"spring\",\n        stiffness: 500,\n        damping: 28,\n        mass: 0.5\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-5 h-5 border-2 border-cyan-400 rounded-full relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 border border-cyan-400 rounded-full animate-ping opacity-75\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-1 bg-cyan-400 rounded-full opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), trails.map((trail, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"fixed pointer-events-none z-[9998]\",\n      initial: {\n        x: trail.x - 2,\n        y: trail.y - 2,\n        opacity: 0.8,\n        scale: 1\n      },\n      animate: {\n        opacity: 0,\n        scale: 0.5\n      },\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-1 h-1 bg-cyan-400 rounded-full\",\n        style: {\n          opacity: Math.max(0, 1 - index * 0.15),\n          transform: `scale(${Math.max(0.3, 1 - index * 0.1)})`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)\n    }, trail.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this)), isHovering && /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"fixed pointer-events-none z-[9997]\",\n      animate: {\n        x: mousePosition.x - 25,\n        y: mousePosition.y - 25\n      },\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 30\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-12 h-12 border border-cyan-400/30 rounded-full animate-pulse\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full border border-cyan-400/20 rounded-full animate-ping\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(CustomCursor, \"KPu1l+RD4efi1x9vjBf3aGKG9GE=\");\n_c = CustomCursor;\nexport default CustomCursor;\nvar _c;\n$RefreshReg$(_c, \"CustomCursor\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomCursor", "_s", "mousePosition", "setMousePosition", "x", "y", "isHovering", "setIsHovering", "trails", "setTrails", "trailId", "updateMousePosition", "e", "newPosition", "clientX", "clientY", "prev", "newTrail", "id", "updatedTrails", "slice", "handleMouseEnter", "handleMouseLeave", "interactiveElements", "document", "querySelectorAll", "for<PERSON>ach", "el", "addEventListener", "window", "removeEventListener", "children", "div", "className", "animate", "scale", "transition", "type", "stiffness", "damping", "mass", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "trail", "index", "initial", "opacity", "duration", "ease", "style", "Math", "max", "transform", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/src/components/layout/CustomCursor.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\n\nconst CustomCursor: React.FC = () => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n  const [trails, setTrails] = useState<Array<{ x: number; y: number; id: number }>>([]);\n\n  useEffect(() => {\n    let trailId = 0;\n    \n    const updateMousePosition = (e: MouseEvent) => {\n      const newPosition = { x: e.clientX, y: e.clientY };\n      setMousePosition(newPosition);\n      \n      // Add trail\n      setTrails(prev => {\n        const newTrail = { ...newPosition, id: trailId++ };\n        const updatedTrails = [newTrail, ...prev.slice(0, 8)];\n        return updatedTrails;\n      });\n    };\n\n    const handleMouseEnter = () => setIsHovering(true);\n    const handleMouseLeave = () => setIsHovering(false);\n\n    // Add event listeners to interactive elements\n    const interactiveElements = document.querySelectorAll('a, button, [role=\"button\"], input, textarea, select');\n    \n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter);\n      el.addEventListener('mouseleave', handleMouseLeave);\n    });\n\n    window.addEventListener('mousemove', updateMousePosition);\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition);\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter);\n        el.removeEventListener('mouseleave', handleMouseLeave);\n      });\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"fixed pointer-events-none z-[9999] mix-blend-mode-difference\"\n        animate={{\n          x: mousePosition.x - 10,\n          y: mousePosition.y - 10,\n          scale: isHovering ? 1.5 : 1,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 500,\n          damping: 28,\n          mass: 0.5,\n        }}\n      >\n        <div className=\"w-5 h-5 border-2 border-cyan-400 rounded-full relative\">\n          <div className=\"absolute inset-0 border border-cyan-400 rounded-full animate-ping opacity-75\"></div>\n          <div className=\"absolute inset-1 bg-cyan-400 rounded-full opacity-50\"></div>\n        </div>\n      </motion.div>\n\n      {/* Cursor trails */}\n      {trails.map((trail, index) => (\n        <motion.div\n          key={trail.id}\n          className=\"fixed pointer-events-none z-[9998]\"\n          initial={{\n            x: trail.x - 2,\n            y: trail.y - 2,\n            opacity: 0.8,\n            scale: 1,\n          }}\n          animate={{\n            opacity: 0,\n            scale: 0.5,\n          }}\n          transition={{\n            duration: 0.8,\n            ease: \"easeOut\",\n          }}\n        >\n          <div \n            className=\"w-1 h-1 bg-cyan-400 rounded-full\"\n            style={{\n              opacity: Math.max(0, 1 - index * 0.15),\n              transform: `scale(${Math.max(0.3, 1 - index * 0.1)})`,\n            }}\n          />\n        </motion.div>\n      ))}\n\n      {/* Hover effect */}\n      {isHovering && (\n        <motion.div\n          className=\"fixed pointer-events-none z-[9997]\"\n          animate={{\n            x: mousePosition.x - 25,\n            y: mousePosition.y - 25,\n          }}\n          transition={{\n            type: \"spring\",\n            stiffness: 300,\n            damping: 30,\n          }}\n        >\n          <div className=\"w-12 h-12 border border-cyan-400/30 rounded-full animate-pulse\">\n            <div className=\"w-full h-full border border-cyan-400/20 rounded-full animate-ping\"></div>\n          </div>\n        </motion.div>\n      )}\n    </>\n  );\n};\n\nexport default CustomCursor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGT,QAAQ,CAAC;IAAEU,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAA8C,EAAE,CAAC;EAErFD,SAAS,CAAC,MAAM;IACd,IAAIiB,OAAO,GAAG,CAAC;IAEf,MAAMC,mBAAmB,GAAIC,CAAa,IAAK;MAC7C,MAAMC,WAAW,GAAG;QAAET,CAAC,EAAEQ,CAAC,CAACE,OAAO;QAAET,CAAC,EAAEO,CAAC,CAACG;MAAQ,CAAC;MAClDZ,gBAAgB,CAACU,WAAW,CAAC;;MAE7B;MACAJ,SAAS,CAACO,IAAI,IAAI;QAChB,MAAMC,QAAQ,GAAG;UAAE,GAAGJ,WAAW;UAAEK,EAAE,EAAER,OAAO;QAAG,CAAC;QAClD,MAAMS,aAAa,GAAG,CAACF,QAAQ,EAAE,GAAGD,IAAI,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,OAAOD,aAAa;MACtB,CAAC,CAAC;IACJ,CAAC;IAED,MAAME,gBAAgB,GAAGA,CAAA,KAAMd,aAAa,CAAC,IAAI,CAAC;IAClD,MAAMe,gBAAgB,GAAGA,CAAA,KAAMf,aAAa,CAAC,KAAK,CAAC;;IAEnD;IACA,MAAMgB,mBAAmB,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,qDAAqD,CAAC;IAE5GF,mBAAmB,CAACG,OAAO,CAACC,EAAE,IAAI;MAChCA,EAAE,CAACC,gBAAgB,CAAC,YAAY,EAAEP,gBAAgB,CAAC;MACnDM,EAAE,CAACC,gBAAgB,CAAC,YAAY,EAAEN,gBAAgB,CAAC;IACrD,CAAC,CAAC;IAEFO,MAAM,CAACD,gBAAgB,CAAC,WAAW,EAAEjB,mBAAmB,CAAC;IAEzD,OAAO,MAAM;MACXkB,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAEnB,mBAAmB,CAAC;MAC5DY,mBAAmB,CAACG,OAAO,CAACC,EAAE,IAAI;QAChCA,EAAE,CAACG,mBAAmB,CAAC,YAAY,EAAET,gBAAgB,CAAC;QACtDM,EAAE,CAACG,mBAAmB,CAAC,YAAY,EAAER,gBAAgB,CAAC;MACxD,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEzB,OAAA,CAAAE,SAAA;IAAAgC,QAAA,gBAEElC,OAAA,CAACF,MAAM,CAACqC,GAAG;MACTC,SAAS,EAAC,8DAA8D;MACxEC,OAAO,EAAE;QACP9B,CAAC,EAAEF,aAAa,CAACE,CAAC,GAAG,EAAE;QACvBC,CAAC,EAAEH,aAAa,CAACG,CAAC,GAAG,EAAE;QACvB8B,KAAK,EAAE7B,UAAU,GAAG,GAAG,GAAG;MAC5B,CAAE;MACF8B,UAAU,EAAE;QACVC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE;MACR,CAAE;MAAAT,QAAA,eAEFlC,OAAA;QAAKoC,SAAS,EAAC,wDAAwD;QAAAF,QAAA,gBACrElC,OAAA;UAAKoC,SAAS,EAAC;QAA8E;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpG/C,OAAA;UAAKoC,SAAS,EAAC;QAAsD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGZpC,MAAM,CAACqC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBlD,OAAA,CAACF,MAAM,CAACqC,GAAG;MAETC,SAAS,EAAC,oCAAoC;MAC9Ce,OAAO,EAAE;QACP5C,CAAC,EAAE0C,KAAK,CAAC1C,CAAC,GAAG,CAAC;QACdC,CAAC,EAAEyC,KAAK,CAACzC,CAAC,GAAG,CAAC;QACd4C,OAAO,EAAE,GAAG;QACZd,KAAK,EAAE;MACT,CAAE;MACFD,OAAO,EAAE;QACPe,OAAO,EAAE,CAAC;QACVd,KAAK,EAAE;MACT,CAAE;MACFC,UAAU,EAAE;QACVc,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE;MACR,CAAE;MAAApB,QAAA,eAEFlC,OAAA;QACEoC,SAAS,EAAC,kCAAkC;QAC5CmB,KAAK,EAAE;UACLH,OAAO,EAAEI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGP,KAAK,GAAG,IAAI,CAAC;UACtCQ,SAAS,EAAE,SAASF,IAAI,CAACC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAGP,KAAK,GAAG,GAAG,CAAC;QACpD;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAvBGE,KAAK,CAAC5B,EAAE;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwBH,CACb,CAAC,EAGDtC,UAAU,iBACTT,OAAA,CAACF,MAAM,CAACqC,GAAG;MACTC,SAAS,EAAC,oCAAoC;MAC9CC,OAAO,EAAE;QACP9B,CAAC,EAAEF,aAAa,CAACE,CAAC,GAAG,EAAE;QACvBC,CAAC,EAAEH,aAAa,CAACG,CAAC,GAAG;MACvB,CAAE;MACF+B,UAAU,EAAE;QACVC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE,GAAG;QACdC,OAAO,EAAE;MACX,CAAE;MAAAR,QAAA,eAEFlC,OAAA;QAAKoC,SAAS,EAAC,gEAAgE;QAAAF,QAAA,eAC7ElC,OAAA;UAAKoC,SAAS,EAAC;QAAmE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA,eACD,CAAC;AAEP,CAAC;AAAC3C,EAAA,CApHID,YAAsB;AAAAwD,EAAA,GAAtBxD,YAAsB;AAsH5B,eAAeA,YAAY;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}