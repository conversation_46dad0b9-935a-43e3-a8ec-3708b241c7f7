{"ast": null, "code": "import { warnOnce } from 'motion-utils';\nimport { updateScrollInfo } from './info.mjs';\nimport { resolveOffsets } from './offsets/index.mjs';\nfunction measure(container, target = container, info) {\n  /**\n   * Find inset of target within scrollable container\n   */\n  info.x.targetOffset = 0;\n  info.y.targetOffset = 0;\n  if (target !== container) {\n    let node = target;\n    while (node && node !== container) {\n      info.x.targetOffset += node.offsetLeft;\n      info.y.targetOffset += node.offsetTop;\n      node = node.offsetParent;\n    }\n  }\n  info.x.targetLength = target === container ? target.scrollWidth : target.clientWidth;\n  info.y.targetLength = target === container ? target.scrollHeight : target.clientHeight;\n  info.x.containerLength = container.clientWidth;\n  info.y.containerLength = container.clientHeight;\n  /**\n   * In development mode ensure scroll containers aren't position: static as this makes\n   * it difficult to measure their relative positions.\n   */\n  if (process.env.NODE_ENV !== \"production\") {\n    if (container && target && target !== container) {\n      warnOnce(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n    }\n  }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n  return {\n    measure: time => {\n      measure(element, options.target, info);\n      updateScrollInfo(element, info, time);\n      if (options.offset || options.target) {\n        resolveOffsets(element, info, options);\n      }\n    },\n    notify: () => onScroll(info)\n  };\n}\nexport { createOnScrollHandler };", "map": {"version": 3, "names": ["warnOnce", "updateScrollInfo", "resolveOffsets", "measure", "container", "target", "info", "x", "targetOffset", "y", "node", "offsetLeft", "offsetTop", "offsetParent", "targetLength", "scrollWidth", "clientWidth", "scrollHeight", "clientHeight", "containerLength", "process", "env", "NODE_ENV", "getComputedStyle", "position", "createOnScrollHandler", "element", "onScroll", "options", "time", "offset", "notify"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs"], "sourcesContent": ["import { warnOnce } from 'motion-utils';\nimport { updateScrollInfo } from './info.mjs';\nimport { resolveOffsets } from './offsets/index.mjs';\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (process.env.NODE_ENV !== \"production\") {\n        if (container && target && target !== container) {\n            warnOnce(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: (time) => {\n            measure(element, options.target, info);\n            updateScrollInfo(element, info, time);\n            if (options.offset || options.target) {\n                resolveOffsets(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\nexport { createOnScrollHandler };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,gBAAgB,QAAQ,YAAY;AAC7C,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,SAASC,OAAOA,CAACC,SAAS,EAAEC,MAAM,GAAGD,SAAS,EAAEE,IAAI,EAAE;EAClD;AACJ;AACA;EACIA,IAAI,CAACC,CAAC,CAACC,YAAY,GAAG,CAAC;EACvBF,IAAI,CAACG,CAAC,CAACD,YAAY,GAAG,CAAC;EACvB,IAAIH,MAAM,KAAKD,SAAS,EAAE;IACtB,IAAIM,IAAI,GAAGL,MAAM;IACjB,OAAOK,IAAI,IAAIA,IAAI,KAAKN,SAAS,EAAE;MAC/BE,IAAI,CAACC,CAAC,CAACC,YAAY,IAAIE,IAAI,CAACC,UAAU;MACtCL,IAAI,CAACG,CAAC,CAACD,YAAY,IAAIE,IAAI,CAACE,SAAS;MACrCF,IAAI,GAAGA,IAAI,CAACG,YAAY;IAC5B;EACJ;EACAP,IAAI,CAACC,CAAC,CAACO,YAAY,GACfT,MAAM,KAAKD,SAAS,GAAGC,MAAM,CAACU,WAAW,GAAGV,MAAM,CAACW,WAAW;EAClEV,IAAI,CAACG,CAAC,CAACK,YAAY,GACfT,MAAM,KAAKD,SAAS,GAAGC,MAAM,CAACY,YAAY,GAAGZ,MAAM,CAACa,YAAY;EACpEZ,IAAI,CAACC,CAAC,CAACY,eAAe,GAAGf,SAAS,CAACY,WAAW;EAC9CV,IAAI,CAACG,CAAC,CAACU,eAAe,GAAGf,SAAS,CAACc,YAAY;EAC/C;AACJ;AACA;AACA;EACI,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACvC,IAAIlB,SAAS,IAAIC,MAAM,IAAIA,MAAM,KAAKD,SAAS,EAAE;MAC7CJ,QAAQ,CAACuB,gBAAgB,CAACnB,SAAS,CAAC,CAACoB,QAAQ,KAAK,QAAQ,EAAE,sJAAsJ,CAAC;IACvN;EACJ;AACJ;AACA,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,QAAQ,EAAErB,IAAI,EAAEsB,OAAO,GAAG,CAAC,CAAC,EAAE;EAClE,OAAO;IACHzB,OAAO,EAAG0B,IAAI,IAAK;MACf1B,OAAO,CAACuB,OAAO,EAAEE,OAAO,CAACvB,MAAM,EAAEC,IAAI,CAAC;MACtCL,gBAAgB,CAACyB,OAAO,EAAEpB,IAAI,EAAEuB,IAAI,CAAC;MACrC,IAAID,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACvB,MAAM,EAAE;QAClCH,cAAc,CAACwB,OAAO,EAAEpB,IAAI,EAAEsB,OAAO,CAAC;MAC1C;IACJ,CAAC;IACDG,MAAM,EAAEA,CAAA,KAAMJ,QAAQ,CAACrB,IAAI;EAC/B,CAAC;AACL;AAEA,SAASmB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}