{"ast": null, "code": "function filterViewAnimations(animation) {\n  const {\n    effect\n  } = animation;\n  if (!effect) return false;\n  return effect.target === document.documentElement && effect.pseudoElement?.startsWith(\"::view-transition\");\n}\nfunction getViewAnimations() {\n  return document.getAnimations().filter(filterViewAnimations);\n}\nexport { getViewAnimations };", "map": {"version": 3, "names": ["filterViewAnimations", "animation", "effect", "target", "document", "documentElement", "pseudoElement", "startsWith", "getViewAnimations", "getAnimations", "filter"], "sources": ["C:/Users/<USER>/Desktop/naman's portfolio/naman-portfolio/node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs"], "sourcesContent": ["function filterViewAnimations(animation) {\n    const { effect } = animation;\n    if (!effect)\n        return false;\n    return (effect.target === document.documentElement &&\n        effect.pseudoElement?.startsWith(\"::view-transition\"));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\nexport { getViewAnimations };\n"], "mappings": "AAAA,SAASA,oBAAoBA,CAACC,SAAS,EAAE;EACrC,MAAM;IAAEC;EAAO,CAAC,GAAGD,SAAS;EAC5B,IAAI,CAACC,MAAM,EACP,OAAO,KAAK;EAChB,OAAQA,MAAM,CAACC,MAAM,KAAKC,QAAQ,CAACC,eAAe,IAC9CH,MAAM,CAACI,aAAa,EAAEC,UAAU,CAAC,mBAAmB,CAAC;AAC7D;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAOJ,QAAQ,CAACK,aAAa,CAAC,CAAC,CAACC,MAAM,CAACV,oBAAoB,CAAC;AAChE;AAEA,SAASQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}